// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');

async function checkSessions() {
  console.log('🔍 Checking Database Sessions...\n');
  
  try {
    const db = getDatabase();
    
    // Check if user_sessions table exists
    const tables = db.db.prepare(`
      SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%session%'
    `).all();

    console.log('Session-related tables:', tables);

    // Get all sessions
    const sessions = db.db.prepare(`
      SELECT s.*, u.username
      FROM user_sessions s
      JOIN users u ON s.user_id = u.id
      ORDER BY s.created_at DESC
    `).all();
    
    console.log(`Found ${sessions.length} sessions in database:`);
    sessions.forEach((session, index) => {
      console.log(`${index + 1}. User: ${session.username} (ID: ${session.user_id})`);
      console.log(`   Session ID: ${session.id}`);
      console.log(`   Token Hash: ${session.token_hash.substring(0, 20)}...`);
      console.log(`   Created: ${session.created_at}`);
      console.log(`   Expires: ${session.expires_at}`);
      console.log(`   IP: ${session.ip_address}`);
      console.log('');
    });
    
    // Test the specific tokens
    const auth = getAuthManager();
    
    console.log('🧪 Testing token validation...\n');
    
    // Test the failing token
    const failingToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEwLCJ1c2VybmFtZSI6InF1ZXVldGVzdF8xNzUxNzY3MTQxODMwIiwicm9sZSI6InVzZXIiLCJpYXQiOjE3NTE3Njc4MzEsImV4cCI6MTc1MjM3MjYzMX0.XUGz1VseNq_swenKFAs3JclScI9vM_HWeilqK-Jr8b4';
    
    console.log('Testing failing token (queuetest user):');
    try {
      const result = auth.validateSession(failingToken);
      console.log('✅ Token validation successful:', result);
    } catch (error) {
      console.log('❌ Token validation failed:', error.message);
      console.log('Error details:', error);
    }
    
    // Create a fresh token for the test user
    console.log('\n🆕 Creating fresh token for test user...');
    const testUser = db.db.prepare(`
      SELECT * FROM users 
      WHERE username LIKE 'queuetest_%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `).get();
    
    if (testUser) {
      const sessionData = auth.createSession(testUser, '127.0.0.1', 'debug-test');
      console.log(`Fresh token created: ${sessionData.token.substring(0, 20)}...`);
      
      // Test the fresh token immediately
      try {
        const result = auth.validateSession(sessionData.token);
        console.log('✅ Fresh token validation successful:', result);
      } catch (error) {
        console.log('❌ Fresh token validation failed:', error.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  checkSessions();
}

module.exports = { checkSessions };
