let pageContext = '';

chrome.action.onClicked.addListener((tab) => {
    chrome.tabs.sendMessage(tab.id, { action: 'initialize', mode: 'reader' });
});

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'storePageContent') {
        pageContext = request.content;
        console.log('Stored page context:', pageContext); // Debug log
        sendResponse({ success: true });
        return true;
    } else if (request.action === 'solveWithGemini') {
        handleGeminiRequest(request, sender);
        return true;
    }
});

async function handleGeminiRequest(request, sender) {
    try {
        const mode = request.mode;
        const imageData = request.imageData;
        const textContent = request.textContent;
        const answerOnly = request.answerOnly;
        let prompt;

        if (mode === 'reader') {
            // Always use the stored pageContext for reader mode
            console.log('Using stored context:', pageContext); // Debug log
            prompt = `Here is the context from the story:\n${pageContext}\n\nNow, please answer this question based on the story above:\n${textContent}\n\n${answerOnly ? 'Give ONLY the direct answer without explanation.' : 'Provide a detailed explanation with evidence from the story.'}`;
        } else if (mode === 'maths') {
            prompt = answerOnly ? 
                "Give ONLY the final answer to this math problem without any steps or explanation." :
                "Solve this math problem step by step, showing all work clearly. Format each step on a new line, starting with 'Step 1:', 'Step 2:', etc.";
        } else if (mode === 'science') {
            prompt = answerOnly ?
                "Give ONLY the final answer to this science question without explanation." :
                "Explain this scientific concept or solve this problem step by step. Break down the explanation into clear, numbered steps.";
        }

        const solution = await handleGeminiSolve(imageData, textContent, prompt);
        chrome.tabs.sendMessage(sender.tab.id, {
            action: 'solutionReady',
            solution: solution
        });
    } catch (error) {
        chrome.tabs.sendMessage(sender.tab.id, {
            action: 'solutionError',
            error: error.message
        });
    }
}

async function handleGeminiSolve(imageData, textContent, prompt) {
    const API_KEY = 'AIzaSyAdbFHKgcsOz9YweT0fZCwJbNODoEwSGzs';
    const API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';

    try {
        let requestBody;
        
        if (imageData) {
            const base64Data = imageData.replace(/^data:image\/\w+;base64,/, '');
            requestBody = {
                contents: [{
                    parts: [
                        { text: prompt },
                        { inlineData: { mimeType: "image/jpeg", data: base64Data } }
                    ]
                }]
            };
        } else if (textContent) {
            // For text content, include everything in a single text part
            requestBody = {
                contents: [{
                    parts: [
                        { text: prompt }
                    ]
                }]
            };
        } else {
            throw new Error('No content provided');
        }

        // Add generation config
        requestBody.generationConfig = {
            temperature: 0.1,
            topK: 1,
            topP: 1,
            maxOutputTokens: 2048
        };

        console.log('Sending request to Gemini:', { prompt, textContent }); // Debug log

        const response = await fetch(`${API_ENDPOINT}?key=${API_KEY}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        const data = await response.json();
        
        if (!response.ok) {
            console.error('Sparxext API error response:', data);
            if (data.error?.message?.includes('API key')) {
                throw new Error('Please update the extention, redownload it or contact Sparxext Discord.');
            }
            throw new Error(data.error?.message || `HTTP error ${response.status}`);
        }

        if (!data.candidates || data.candidates.length === 0) {
            console.error('No candidates in response:', data);
            throw new Error('No solution generated');
        }

        return data.candidates[0].content.parts[0].text;
    } catch (error) {
        console.error('Sparxext API error:', error);
        throw new Error(`Failed to get solution from Sparxext`);
    }
}