import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getWebhookManager } from '../../../../lib/webhook';

// Middleware wrapper for admin authentication
function withAdminAuth(handler) {
  return async (request, context) => {
    try {
      const db = getDatabase();
      const authHeader = request.headers.get('authorization');
      
      if (!authHeader?.startsWith('Bearer ')) {
        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }
      
      const token = authHeader.substring(7);
      const session = db.validateSession(token);
      
      if (!session || session.role !== 'admin') {
        return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
      }
      
      const req = {
        ...request,
        body: await request.json().catch(() => ({})),
        user: {
          id: session.user_id,
          username: session.username,
          role: session.role
        }
      };

      return handler(req, context);
    } catch (error) {
      console.error('Admin auth error:', error);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }
  };
}

// GET - Get license configurations
export async function GET(request) {
  const handler = withAdminAuth(async (req) => {
    try {
      const db = getDatabase();
      const url = new URL(request.url);
      const licenseKeyId = url.searchParams.get('license_key_id');
      const keyCode = url.searchParams.get('key_code');

      if (licenseKeyId) {
        // Get specific license configuration
        const features = db.getLicenseFeatures(parseInt(licenseKeyId));
        const licenseInfo = db.db.prepare(`
          SELECT lk.*, u.username as created_by_username
          FROM license_keys lk
          LEFT JOIN users u ON lk.created_by = u.id
          WHERE lk.id = ?
        `).get(licenseKeyId);

        if (!licenseInfo) {
          return NextResponse.json({
            error: 'License key not found'
          }, { status: 404 });
        }

        return NextResponse.json({
          license: {
            ...licenseInfo,
            features: JSON.parse(licenseInfo.features || '[]')
          },
          queue_features: features
        });

      } else if (keyCode) {
        // Get license by key code
        const licenseInfo = db.db.prepare(`
          SELECT lk.*, u.username as created_by_username
          FROM license_keys lk
          LEFT JOIN users u ON lk.created_by = u.id
          WHERE lk.key_code = ?
        `).get(keyCode);

        if (!licenseInfo) {
          return NextResponse.json({
            error: 'License key not found'
          }, { status: 404 });
        }

        const features = db.getLicenseFeatures(licenseInfo.id);

        return NextResponse.json({
          license: {
            ...licenseInfo,
            features: JSON.parse(licenseInfo.features || '[]')
          },
          queue_features: features
        });

      } else {
        // Get all license configurations
        const licenses = db.getLicenseKeys(100, 0);
        const licensesWithFeatures = licenses.map(license => ({
          ...license,
          features: JSON.parse(license.features || '[]'),
          queue_features: db.getLicenseFeatures(license.id)
        }));

        return NextResponse.json({
          licenses: licensesWithFeatures,
          total: licensesWithFeatures.length
        });
      }

    } catch (error) {
      console.error('Get license config error:', error);
      return NextResponse.json({
        error: 'Failed to retrieve license configuration',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}

// PATCH - Update license configuration
export async function PATCH(request) {
  const handler = withAdminAuth(async (req) => {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const { 
        license_key_id, 
        key_code, 
        queue_features,
        license_features 
      } = req.body;

      // Find license by ID or key code
      let licenseId = license_key_id;
      if (!licenseId && key_code) {
        const license = db.db.prepare('SELECT id FROM license_keys WHERE key_code = ?').get(key_code);
        if (!license) {
          return NextResponse.json({
            error: 'License key not found'
          }, { status: 404 });
        }
        licenseId = license.id;
      }

      if (!licenseId) {
        return NextResponse.json({
          error: 'license_key_id or key_code is required'
        }, { status: 400 });
      }

      // Get current configuration for comparison
      const currentFeatures = db.getLicenseFeatures(licenseId);
      const licenseInfo = db.db.prepare('SELECT key_code FROM license_keys WHERE id = ?').get(licenseId);

      // Update queue features if provided
      if (queue_features) {
        // Validate queue feature values
        const { max_accounts_per_batch, priority_level, scheduling_access } = queue_features;

        if (max_accounts_per_batch !== undefined && (max_accounts_per_batch < 0 || max_accounts_per_batch > 1000)) {
          return NextResponse.json({
            error: 'max_accounts_per_batch must be between 0 and 1000'
          }, { status: 400 });
        }

        if (priority_level !== undefined && (priority_level < 0 || priority_level > 10)) {
          return NextResponse.json({
            error: 'priority_level must be between 0 and 10'
          }, { status: 400 });
        }

        // Update license features
        db.setLicenseFeatures(licenseId, {
          max_accounts_per_batch: max_accounts_per_batch !== undefined ? max_accounts_per_batch : currentFeatures.max_accounts_per_batch,
          priority_level: priority_level !== undefined ? priority_level : currentFeatures.priority_level,
          scheduling_access: scheduling_access !== undefined ? scheduling_access : currentFeatures.scheduling_access
        });

        // Send webhook notification for feature changes
        const changes = [];
        if (max_accounts_per_batch !== undefined && max_accounts_per_batch !== currentFeatures.max_accounts_per_batch) {
          changes.push(`Batch limit: ${currentFeatures.max_accounts_per_batch} → ${max_accounts_per_batch}`);
        }
        if (priority_level !== undefined && priority_level !== currentFeatures.priority_level) {
          changes.push(`Priority level: ${currentFeatures.priority_level} → ${priority_level}`);
        }
        if (scheduling_access !== undefined && scheduling_access !== currentFeatures.scheduling_access) {
          changes.push(`Scheduling: ${currentFeatures.scheduling_access ? 'enabled' : 'disabled'} → ${scheduling_access ? 'enabled' : 'disabled'}`);
        }

        if (changes.length > 0) {
          await webhook.sendQueueAlert(
            'info',
            'License configuration updated',
            {
              license_key: licenseInfo.key_code,
              admin_user: req.user.username,
              changes: changes.join(', ')
            }
          );
        }
      }

      // Update general license features if provided
      if (license_features && Array.isArray(license_features)) {
        const updateStmt = db.db.prepare('UPDATE license_keys SET features = ? WHERE id = ?');
        updateStmt.run(JSON.stringify(license_features), licenseId);
      }

      // Log the configuration change
      db.logActivity(
        req.user.id,
        'LICENSE_CONFIG_UPDATED',
        `Updated configuration for license ${licenseInfo.key_code}`
      );

      // Get updated configuration
      const updatedFeatures = db.getLicenseFeatures(licenseId);
      const updatedLicense = db.db.prepare(`
        SELECT lk.*, u.username as created_by_username
        FROM license_keys lk
        LEFT JOIN users u ON lk.created_by = u.id
        WHERE lk.id = ?
      `).get(licenseId);

      return NextResponse.json({
        success: true,
        message: 'License configuration updated successfully',
        license: {
          ...updatedLicense,
          features: JSON.parse(updatedLicense.features || '[]')
        },
        queue_features: updatedFeatures,
        changes_applied: queue_features ? Object.keys(queue_features) : []
      });

    } catch (error) {
      console.error('Update license config error:', error);
      
      const webhook = getWebhookManager();
      await webhook.sendQueueAlert(
        'system_error',
        'License configuration update failed',
        {
          admin_user: req.user?.username || 'unknown',
          error: error.message
        }
      );

      return NextResponse.json({
        error: 'Failed to update license configuration',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}

// POST - Bulk update license configurations
export async function POST(request) {
  const handler = withAdminAuth(async (req) => {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const { bulk_updates } = req.body;

      if (!bulk_updates || !Array.isArray(bulk_updates)) {
        return NextResponse.json({
          error: 'bulk_updates array is required'
        }, { status: 400 });
      }

      const results = [];
      const transaction = db.db.transaction(() => {
        for (const update of bulk_updates) {
          try {
            const { license_key_id, key_code, queue_features } = update;
            
            // Find license
            let licenseId = license_key_id;
            if (!licenseId && key_code) {
              const license = db.db.prepare('SELECT id FROM license_keys WHERE key_code = ?').get(key_code);
              if (license) {
                licenseId = license.id;
              }
            }

            if (!licenseId) {
              results.push({
                license_key_id,
                key_code,
                success: false,
                error: 'License not found'
              });
              continue;
            }

            // Update features
            if (queue_features) {
              const currentFeatures = db.getLicenseFeatures(licenseId);
              db.setLicenseFeatures(licenseId, {
                max_accounts_per_batch: queue_features.max_accounts_per_batch !== undefined ? 
                  queue_features.max_accounts_per_batch : currentFeatures.max_accounts_per_batch,
                priority_level: queue_features.priority_level !== undefined ? 
                  queue_features.priority_level : currentFeatures.priority_level,
                scheduling_access: queue_features.scheduling_access !== undefined ? 
                  queue_features.scheduling_access : currentFeatures.scheduling_access
              });
            }

            results.push({
              license_key_id: licenseId,
              key_code,
              success: true,
              updated_features: queue_features
            });

          } catch (error) {
            results.push({
              license_key_id: update.license_key_id,
              key_code: update.key_code,
              success: false,
              error: error.message
            });
          }
        }
      });

      transaction();

      // Send webhook notification
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      await webhook.sendQueueAlert(
        'info',
        'Bulk license configuration update completed',
        {
          admin_user: req.user.username,
          total_updates: bulk_updates.length,
          successful: successCount,
          failed: failureCount
        }
      );

      // Log activity
      db.logActivity(
        req.user.id,
        'BULK_LICENSE_CONFIG_UPDATE',
        `Updated ${successCount} licenses, ${failureCount} failed`
      );

      return NextResponse.json({
        success: true,
        message: `Bulk update completed: ${successCount} successful, ${failureCount} failed`,
        results,
        summary: {
          total: bulk_updates.length,
          successful: successCount,
          failed: failureCount
        }
      });

    } catch (error) {
      console.error('Bulk license config update error:', error);
      return NextResponse.json({
        error: 'Bulk update failed',
        details: error.message
      }, { status: 500 });
    }
  });

  return handler(request);
}