const { getDatabase } = require('./database');
const { getWebhookManager } = require('./webhook');

class QueueMiddleware {
  static async validateLicenseFeatures(req, res, next) {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({ error: 'Authentication required' });
      }

      // Get user's license features
      const features = db.getUserLicenseFeatures(userId);
      req.licenseFeatures = features;

      // Check specific feature requirements based on the endpoint
      const endpoint = req.route?.path || req.path || req.url || '';

      if (endpoint.includes('/batch')) {
        // Validate batch processing access
        if (features.max_accounts_per_batch === 0) {
          await webhook.sendLicenseViolation(
            req.user.username,
            'Batch Processing Access Denied',
            'User attempted to access batch processing without proper license'
          );
          
          return res.status(403).json({ 
            error: 'Batch processing not available for your license',
            feature: 'max_accounts_per_batch',
            current_limit: 0
          });
        }
      }

      if (endpoint.includes('/schedule')) {
        // Validate scheduling access
        if (!features.scheduling_access) {
          await webhook.sendLicenseViolation(
            req.user.username,
            'Scheduling Access Denied',
            'User attempted to access scheduling without proper license'
          );
          
          return res.status(403).json({ 
            error: 'Scheduling not available for your license',
            feature: 'scheduling_access',
            current_access: false
          });
        }
      }

      next();
    } catch (error) {
      console.error('License validation error:', error);
      res.status(500).json({ error: 'License validation failed' });
    }
  }

  static async validateBatchSize(req, res, next) {
    try {
      const accounts = req.body.accounts || [];
      const maxAccounts = req.licenseFeatures?.max_accounts_per_batch || 0;

      if (maxAccounts > 0 && accounts.length > maxAccounts) {
        const webhook = getWebhookManager();
        await webhook.sendLicenseViolation(
          req.user.username,
          'Batch Size Limit Exceeded',
          `Attempted to submit ${accounts.length} accounts, limit is ${maxAccounts}`
        );

        return res.status(403).json({
          error: 'Batch size exceeds license limit',
          submitted_count: accounts.length,
          max_allowed: maxAccounts
        });
      }

      next();
    } catch (error) {
      console.error('Batch size validation error:', error);
      res.status(500).json({ error: 'Batch size validation failed' });
    }
  }

  static async validateDailyBatchLimit(req, res, next) {
    try {
      const db = getDatabase();
      const userId = req.user.id;
      const features = req.licenseFeatures;

      // Check daily batch count
      const dailyBatchCount = db.getUserDailyBatchCount(userId);
      const maxBatchesPerDay = features.max_batches_per_day || 1;

      if (dailyBatchCount >= maxBatchesPerDay) {
        const webhook = getWebhookManager();
        await webhook.sendLicenseViolation(
          req.user.username,
          'Daily Batch Limit Exceeded',
          `Attempted to create batch ${dailyBatchCount + 1}, daily limit is ${maxBatchesPerDay}`
        );

        return res.status(403).json({
          error: 'Daily batch limit reached',
          current_count: dailyBatchCount,
          max_allowed: maxBatchesPerDay,
          message: `You have reached your daily limit of ${maxBatchesPerDay} batch${maxBatchesPerDay > 1 ? 'es' : ''}. Please try again tomorrow.`
        });
      }

      next();
    } catch (error) {
      console.error('Daily batch limit validation error:', error);
      res.status(500).json({ error: 'Daily batch limit validation failed' });
    }
  }

  static async validatePriorityLevel(req, res, next) {
    try {
      const requestedPriority = req.body.priority_level;
      const maxPriority = req.licenseFeatures?.priority_level || 0;

      if (requestedPriority !== undefined && requestedPriority > maxPriority) {
        const webhook = getWebhookManager();
        await webhook.sendLicenseViolation(
          req.user.username,
          'Priority Level Exceeded',
          `Attempted to set priority ${requestedPriority}, max allowed is ${maxPriority}`
        );

        return res.status(403).json({
          error: 'Priority level exceeds license limit',
          requested_priority: requestedPriority,
          max_allowed: maxPriority
        });
      }

      next();
    } catch (error) {
      console.error('Priority validation error:', error);
      res.status(500).json({ error: 'Priority validation failed' });
    }
  }

  static async validateWeeklyScheduleLimit(req, res, next) {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const userId = req.user.id;

      // Check weekly schedule count (1 account per week maximum)
      const weeklyScheduleCount = db.getUserWeeklyScheduleCount(userId);
      const maxSchedulesPerWeek = 1; // Fixed limit: 1 account per week

      if (weeklyScheduleCount >= maxSchedulesPerWeek) {
        await webhook.sendLicenseViolation(
          req.user.username,
          'Weekly Schedule Limit Exceeded',
          `Attempted to create schedule ${weeklyScheduleCount + 1}, weekly limit is ${maxSchedulesPerWeek}`
        );

        return res.status(403).json({
          error: 'Weekly schedule limit reached',
          current_count: weeklyScheduleCount,
          max_allowed: maxSchedulesPerWeek,
          message: `You have reached your weekly limit of ${maxSchedulesPerWeek} scheduled account. Please try again next week.`
        });
      }

      next();
    } catch (error) {
      console.error('Weekly schedule limit validation error:', error);
      res.status(500).json({ error: 'Weekly schedule limit validation failed' });
    }
  }

  static async checkScheduleConflicts(req, res, next) {
    try {
      const db = getDatabase();
      const webhook = getWebhookManager();
      const { scheduled_time, duration_minutes = 30 } = req.body;
      const userId = req.user.id;

      if (scheduled_time) {
        const conflicts = db.checkScheduleConflicts(userId, scheduled_time, duration_minutes);

        if (conflicts.length > 0) {
          await webhook.sendScheduleConflict(
            req.user.username,
            scheduled_time,
            `${conflicts.length} conflicting schedule(s) found`
          );

          return res.status(409).json({
            error: 'Schedule conflict detected',
            requested_time: scheduled_time,
            conflicts: conflicts.map(c => ({
              id: c.id,
              scheduled_time: c.scheduled_time,
              duration_minutes: c.duration_minutes
            }))
          });
        }
      }

      next();
    } catch (error) {
      console.error('Schedule conflict check error:', error);
      res.status(500).json({ error: 'Schedule conflict check failed' });
    }
  }

  static async logQueueActivity(req, res, next) {
    try {
      const db = getDatabase();
      const originalSend = res.send;
      
      res.send = function(data) {
        // Log successful queue operations
        if (res.statusCode >= 200 && res.statusCode < 300) {
          const action = req.method + '_' + req.route?.path?.replace(/[\/\:]/g, '_').toUpperCase();
          const details = {
            endpoint: req.originalUrl,
            method: req.method,
            status: res.statusCode
          };
          
          db.logActivity(req.user?.id, action, JSON.stringify(details));
        }
        
        originalSend.call(this, data);
      };

      next();
    } catch (error) {
      console.error('Activity logging error:', error);
      next(); // Don't block the request for logging errors
    }
  }

  static gracefulDegradation(featureCheck) {
    return (req, res, next) => {
      try {
        const hasFeature = featureCheck(req.licenseFeatures);
        
        if (!hasFeature) {
          // Instead of blocking, provide limited functionality
          req.degradedMode = true;
          req.degradationReason = 'License feature not available';
        }
        
        next();
      } catch (error) {
        console.error('Graceful degradation error:', error);
        next();
      }
    };
  }

  static async rateLimitByLicense(req, res, next) {
    try {
      const db = getDatabase();
      const userId = req.user.id;
      const features = req.licenseFeatures;
      
      // Implement rate limiting based on license features
      const recentBatches = db.getQueueBatches(userId, null, 10, 0);
      const recentBatchCount = recentBatches.filter(batch => {
        const batchTime = new Date(batch.created_at);
        const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
        return batchTime > hourAgo;
      }).length;

      // Basic rate limiting: higher priority licenses get more requests
      const maxBatchesPerHour = Math.max(1, features.priority_level);
      
      if (recentBatchCount >= maxBatchesPerHour) {
        return res.status(429).json({
          error: 'Rate limit exceeded',
          limit: maxBatchesPerHour,
          current: recentBatchCount,
          reset_time: new Date(Date.now() + 60 * 60 * 1000).toISOString()
        });
      }

      next();
    } catch (error) {
      console.error('Rate limiting error:', error);
      next(); // Don't block for rate limiting errors
    }
  }
}

module.exports = QueueMiddleware;