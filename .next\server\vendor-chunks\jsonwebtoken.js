"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsonwebtoken";
exports.ids = ["vendor-chunks/jsonwebtoken"];
exports.modules = {

/***/ "(rsc)/./node_modules/jsonwebtoken/decode.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/decode.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nmodule.exports = function(jwt, options) {\n    options = options || {};\n    var decoded = jws.decode(jwt, options);\n    if (!decoded) {\n        return null;\n    }\n    var payload = decoded.payload;\n    //try parse the payload\n    if (typeof payload === \"string\") {\n        try {\n            var obj = JSON.parse(payload);\n            if (obj !== null && typeof obj === \"object\") {\n                payload = obj;\n            }\n        } catch (e) {}\n    }\n    //return header if `complete` option is enabled.  header includes claims\n    //such as `kid` and `alg` used to select the key within a JWKS needed to\n    //verify the signature\n    if (options.complete === true) {\n        return {\n            header: decoded.header,\n            payload: payload,\n            signature: decoded.signature\n        };\n    }\n    return payload;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/decode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/index.js":
/*!********************************************!*\
  !*** ./node_modules/jsonwebtoken/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = {\n    decode: __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\"),\n    verify: __webpack_require__(/*! ./verify */ \"(rsc)/./node_modules/jsonwebtoken/verify.js\"),\n    sign: __webpack_require__(/*! ./sign */ \"(rsc)/./node_modules/jsonwebtoken/sign.js\"),\n    JsonWebTokenError: __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\"),\n    NotBeforeError: __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\"),\n    TokenExpiredError: __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\")\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7QUFBQUEsT0FBT0MsT0FBTyxHQUFHO0lBQ2ZDLFFBQVFDLG1CQUFPQSxDQUFDO0lBQ2hCQyxRQUFRRCxtQkFBT0EsQ0FBQztJQUNoQkUsTUFBTUYsbUJBQU9BLENBQUM7SUFDZEcsbUJBQW1CSCxtQkFBT0EsQ0FBQztJQUMzQkksZ0JBQWdCSixtQkFBT0EsQ0FBQztJQUN4QkssbUJBQW1CTCxtQkFBT0EsQ0FBQztBQUM3QiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9pbmRleC5qcz85YmU4Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xuICBkZWNvZGU6IHJlcXVpcmUoJy4vZGVjb2RlJyksXG4gIHZlcmlmeTogcmVxdWlyZSgnLi92ZXJpZnknKSxcbiAgc2lnbjogcmVxdWlyZSgnLi9zaWduJyksXG4gIEpzb25XZWJUb2tlbkVycm9yOiByZXF1aXJlKCcuL2xpYi9Kc29uV2ViVG9rZW5FcnJvcicpLFxuICBOb3RCZWZvcmVFcnJvcjogcmVxdWlyZSgnLi9saWIvTm90QmVmb3JlRXJyb3InKSxcbiAgVG9rZW5FeHBpcmVkRXJyb3I6IHJlcXVpcmUoJy4vbGliL1Rva2VuRXhwaXJlZEVycm9yJyksXG59O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJkZWNvZGUiLCJyZXF1aXJlIiwidmVyaWZ5Iiwic2lnbiIsIkpzb25XZWJUb2tlbkVycm9yIiwiTm90QmVmb3JlRXJyb3IiLCJUb2tlbkV4cGlyZWRFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/JsonWebTokenError.js ***!
  \************************************************************/
/***/ ((module) => {

eval("\nvar JsonWebTokenError = function(message, error) {\n    Error.call(this, message);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    }\n    this.name = \"JsonWebTokenError\";\n    this.message = message;\n    if (error) this.inner = error;\n};\nJsonWebTokenError.prototype = Object.create(Error.prototype);\nJsonWebTokenError.prototype.constructor = JsonWebTokenError;\nmodule.exports = JsonWebTokenError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsb0JBQW9CLFNBQVVDLE9BQU8sRUFBRUMsS0FBSztJQUM5Q0MsTUFBTUMsSUFBSSxDQUFDLElBQUksRUFBRUg7SUFDakIsSUFBR0UsTUFBTUUsaUJBQWlCLEVBQUU7UUFDMUJGLE1BQU1FLGlCQUFpQixDQUFDLElBQUksRUFBRSxJQUFJLENBQUNDLFdBQVc7SUFDaEQ7SUFDQSxJQUFJLENBQUNDLElBQUksR0FBRztJQUNaLElBQUksQ0FBQ04sT0FBTyxHQUFHQTtJQUNmLElBQUlDLE9BQU8sSUFBSSxDQUFDTSxLQUFLLEdBQUdOO0FBQzFCO0FBRUFGLGtCQUFrQlMsU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNSLE1BQU1NLFNBQVM7QUFDM0RULGtCQUFrQlMsU0FBUyxDQUFDSCxXQUFXLEdBQUdOO0FBRTFDWSxPQUFPQyxPQUFPLEdBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhZGVyLWF1dG8vLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Kc29uV2ViVG9rZW5FcnJvci5qcz8xNmYyIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IGZ1bmN0aW9uIChtZXNzYWdlLCBlcnJvcikge1xuICBFcnJvci5jYWxsKHRoaXMsIG1lc3NhZ2UpO1xuICBpZihFcnJvci5jYXB0dXJlU3RhY2tUcmFjZSkge1xuICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIHRoaXMuY29uc3RydWN0b3IpO1xuICB9XG4gIHRoaXMubmFtZSA9ICdKc29uV2ViVG9rZW5FcnJvcic7XG4gIHRoaXMubWVzc2FnZSA9IG1lc3NhZ2U7XG4gIGlmIChlcnJvcikgdGhpcy5pbm5lciA9IGVycm9yO1xufTtcblxuSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShFcnJvci5wcm90b3R5cGUpO1xuSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gSnNvbldlYlRva2VuRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gSnNvbldlYlRva2VuRXJyb3I7XG4iXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJtZXNzYWdlIiwiZXJyb3IiLCJFcnJvciIsImNhbGwiLCJjYXB0dXJlU3RhY2tUcmFjZSIsImNvbnN0cnVjdG9yIiwibmFtZSIsImlubmVyIiwicHJvdG90eXBlIiwiT2JqZWN0IiwiY3JlYXRlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js":
/*!*********************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/NotBeforeError.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar NotBeforeError = function(message, date) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"NotBeforeError\";\n    this.date = date;\n};\nNotBeforeError.prototype = Object.create(JsonWebTokenError.prototype);\nNotBeforeError.prototype.constructor = NotBeforeError;\nmodule.exports = NotBeforeError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsb0JBQW9CQyxtQkFBT0EsQ0FBQztBQUVoQyxJQUFJQyxpQkFBaUIsU0FBVUMsT0FBTyxFQUFFQyxJQUFJO0lBQzFDSixrQkFBa0JLLElBQUksQ0FBQyxJQUFJLEVBQUVGO0lBQzdCLElBQUksQ0FBQ0csSUFBSSxHQUFHO0lBQ1osSUFBSSxDQUFDRixJQUFJLEdBQUdBO0FBQ2Q7QUFFQUYsZUFBZUssU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNULGtCQUFrQk8sU0FBUztBQUVwRUwsZUFBZUssU0FBUyxDQUFDRyxXQUFXLEdBQUdSO0FBRXZDUyxPQUFPQyxPQUFPLEdBQUdWIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVhZGVyLWF1dG8vLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ob3RCZWZvcmVFcnJvci5qcz84NjY4Il0sInNvdXJjZXNDb250ZW50IjpbInZhciBKc29uV2ViVG9rZW5FcnJvciA9IHJlcXVpcmUoJy4vSnNvbldlYlRva2VuRXJyb3InKTtcblxudmFyIE5vdEJlZm9yZUVycm9yID0gZnVuY3Rpb24gKG1lc3NhZ2UsIGRhdGUpIHtcbiAgSnNvbldlYlRva2VuRXJyb3IuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgdGhpcy5uYW1lID0gJ05vdEJlZm9yZUVycm9yJztcbiAgdGhpcy5kYXRlID0gZGF0ZTtcbn07XG5cbk5vdEJlZm9yZUVycm9yLnByb3RvdHlwZSA9IE9iamVjdC5jcmVhdGUoSnNvbldlYlRva2VuRXJyb3IucHJvdG90eXBlKTtcblxuTm90QmVmb3JlRXJyb3IucHJvdG90eXBlLmNvbnN0cnVjdG9yID0gTm90QmVmb3JlRXJyb3I7XG5cbm1vZHVsZS5leHBvcnRzID0gTm90QmVmb3JlRXJyb3I7Il0sIm5hbWVzIjpbIkpzb25XZWJUb2tlbkVycm9yIiwicmVxdWlyZSIsIk5vdEJlZm9yZUVycm9yIiwibWVzc2FnZSIsImRhdGUiLCJjYWxsIiwibmFtZSIsInByb3RvdHlwZSIsIk9iamVjdCIsImNyZWF0ZSIsImNvbnN0cnVjdG9yIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js":
/*!************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/TokenExpiredError.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar JsonWebTokenError = __webpack_require__(/*! ./JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nvar TokenExpiredError = function(message, expiredAt) {\n    JsonWebTokenError.call(this, message);\n    this.name = \"TokenExpiredError\";\n    this.expiredAt = expiredAt;\n};\nTokenExpiredError.prototype = Object.create(JsonWebTokenError.prototype);\nTokenExpiredError.prototype.constructor = TokenExpiredError;\nmodule.exports = TokenExpiredError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9Ub2tlbkV4cGlyZWRFcnJvci5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsb0JBQW9CQyxtQkFBT0EsQ0FBQztBQUVoQyxJQUFJQyxvQkFBb0IsU0FBVUMsT0FBTyxFQUFFQyxTQUFTO0lBQ2xESixrQkFBa0JLLElBQUksQ0FBQyxJQUFJLEVBQUVGO0lBQzdCLElBQUksQ0FBQ0csSUFBSSxHQUFHO0lBQ1osSUFBSSxDQUFDRixTQUFTLEdBQUdBO0FBQ25CO0FBRUFGLGtCQUFrQkssU0FBUyxHQUFHQyxPQUFPQyxNQUFNLENBQUNULGtCQUFrQk8sU0FBUztBQUV2RUwsa0JBQWtCSyxTQUFTLENBQUNHLFdBQVcsR0FBR1I7QUFFMUNTLE9BQU9DLE9BQU8sR0FBR1YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL1Rva2VuRXhwaXJlZEVycm9yLmpzPzkwZWMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIEpzb25XZWJUb2tlbkVycm9yID0gcmVxdWlyZSgnLi9Kc29uV2ViVG9rZW5FcnJvcicpO1xuXG52YXIgVG9rZW5FeHBpcmVkRXJyb3IgPSBmdW5jdGlvbiAobWVzc2FnZSwgZXhwaXJlZEF0KSB7XG4gIEpzb25XZWJUb2tlbkVycm9yLmNhbGwodGhpcywgbWVzc2FnZSk7XG4gIHRoaXMubmFtZSA9ICdUb2tlbkV4cGlyZWRFcnJvcic7XG4gIHRoaXMuZXhwaXJlZEF0ID0gZXhwaXJlZEF0O1xufTtcblxuVG9rZW5FeHBpcmVkRXJyb3IucHJvdG90eXBlID0gT2JqZWN0LmNyZWF0ZShKc29uV2ViVG9rZW5FcnJvci5wcm90b3R5cGUpO1xuXG5Ub2tlbkV4cGlyZWRFcnJvci5wcm90b3R5cGUuY29uc3RydWN0b3IgPSBUb2tlbkV4cGlyZWRFcnJvcjtcblxubW9kdWxlLmV4cG9ydHMgPSBUb2tlbkV4cGlyZWRFcnJvcjsiXSwibmFtZXMiOlsiSnNvbldlYlRva2VuRXJyb3IiLCJyZXF1aXJlIiwiVG9rZW5FeHBpcmVkRXJyb3IiLCJtZXNzYWdlIiwiZXhwaXJlZEF0IiwiY2FsbCIsIm5hbWUiLCJwcm90b3R5cGUiLCJPYmplY3QiLCJjcmVhdGUiLCJjb25zdHJ1Y3RvciIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js":
/*!************************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=15.7.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9hc3ltbWV0cmljS2V5RGV0YWlsc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsTUFBTUEsU0FBU0MsbUJBQU9BLENBQUM7QUFFdkJDLE9BQU9DLE9BQU8sR0FBR0gsT0FBT0ksU0FBUyxDQUFDQyxRQUFRQyxPQUFPLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL2FzeW1tZXRyaWNLZXlEZXRhaWxzU3VwcG9ydGVkLmpzPzczZDkiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3Qgc2VtdmVyID0gcmVxdWlyZSgnc2VtdmVyJyk7XG5cbm1vZHVsZS5leHBvcnRzID0gc2VtdmVyLnNhdGlzZmllcyhwcm9jZXNzLnZlcnNpb24sICc+PTE1LjcuMCcpO1xuIl0sIm5hbWVzIjpbInNlbXZlciIsInJlcXVpcmUiLCJtb2R1bGUiLCJleHBvcnRzIiwic2F0aXNmaWVzIiwicHJvY2VzcyIsInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js":
/*!******************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/psSupported.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \"^6.12.0 || >=8.0.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9wc1N1cHBvcnRlZC5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsU0FBU0MsbUJBQU9BLENBQUM7QUFFckJDLE9BQU9DLE9BQU8sR0FBR0gsT0FBT0ksU0FBUyxDQUFDQyxRQUFRQyxPQUFPLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZWFkZXItYXV0by8uL25vZGVfbW9kdWxlcy9qc29ud2VidG9rZW4vbGliL3BzU3VwcG9ydGVkLmpzP2M4ZDQiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnXjYuMTIuMCB8fCA+PTguMC4wJyk7XG4iXSwibmFtZXMiOlsic2VtdmVyIiwicmVxdWlyZSIsIm1vZHVsZSIsImV4cG9ydHMiLCJzYXRpc2ZpZXMiLCJwcm9jZXNzIiwidmVyc2lvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js":
/*!********************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst semver = __webpack_require__(/*! semver */ \"(rsc)/./node_modules/semver/index.js\");\nmodule.exports = semver.satisfies(process.version, \">=16.9.0\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi9yc2FQc3NLZXlEZXRhaWxzU3VwcG9ydGVkLmpzIiwibWFwcGluZ3MiOiI7QUFBQSxNQUFNQSxTQUFTQyxtQkFBT0EsQ0FBQztBQUV2QkMsT0FBT0MsT0FBTyxHQUFHSCxPQUFPSSxTQUFTLENBQUNDLFFBQVFDLE9BQU8sRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvcnNhUHNzS2V5RGV0YWlsc1N1cHBvcnRlZC5qcz9mOTA4Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IHNlbXZlciA9IHJlcXVpcmUoJ3NlbXZlcicpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHNlbXZlci5zYXRpc2ZpZXMocHJvY2Vzcy52ZXJzaW9uLCAnPj0xNi45LjAnKTtcbiJdLCJuYW1lcyI6WyJzZW12ZXIiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInNhdGlzZmllcyIsInByb2Nlc3MiLCJ2ZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/timespan.js":
/*!***************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/timespan.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/ms/index.js\");\nmodule.exports = function(time, iat) {\n    var timestamp = iat || Math.floor(Date.now() / 1000);\n    if (typeof time === \"string\") {\n        var milliseconds = ms(time);\n        if (typeof milliseconds === \"undefined\") {\n            return;\n        }\n        return Math.floor(timestamp + milliseconds / 1000);\n    } else if (typeof time === \"number\") {\n        return timestamp + time;\n    } else {\n        return;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvanNvbndlYnRva2VuL2xpYi90aW1lc3Bhbi5qcyIsIm1hcHBpbmdzIjoiO0FBQUEsSUFBSUEsS0FBS0MsbUJBQU9BLENBQUM7QUFFakJDLE9BQU9DLE9BQU8sR0FBRyxTQUFVQyxJQUFJLEVBQUVDLEdBQUc7SUFDbEMsSUFBSUMsWUFBWUQsT0FBT0UsS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUs7SUFFL0MsSUFBSSxPQUFPTixTQUFTLFVBQVU7UUFDNUIsSUFBSU8sZUFBZVgsR0FBR0k7UUFDdEIsSUFBSSxPQUFPTyxpQkFBaUIsYUFBYTtZQUN2QztRQUNGO1FBQ0EsT0FBT0osS0FBS0MsS0FBSyxDQUFDRixZQUFZSyxlQUFlO0lBQy9DLE9BQU8sSUFBSSxPQUFPUCxTQUFTLFVBQVU7UUFDbkMsT0FBT0UsWUFBWUY7SUFDckIsT0FBTztRQUNMO0lBQ0Y7QUFFRiIsInNvdXJjZXMiOlsid2VicGFjazovL3JlYWRlci1hdXRvLy4vbm9kZV9tb2R1bGVzL2pzb253ZWJ0b2tlbi9saWIvdGltZXNwYW4uanM/Y2VmMCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbXMgPSByZXF1aXJlKCdtcycpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uICh0aW1lLCBpYXQpIHtcbiAgdmFyIHRpbWVzdGFtcCA9IGlhdCB8fCBNYXRoLmZsb29yKERhdGUubm93KCkgLyAxMDAwKTtcblxuICBpZiAodHlwZW9mIHRpbWUgPT09ICdzdHJpbmcnKSB7XG4gICAgdmFyIG1pbGxpc2Vjb25kcyA9IG1zKHRpbWUpO1xuICAgIGlmICh0eXBlb2YgbWlsbGlzZWNvbmRzID09PSAndW5kZWZpbmVkJykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICByZXR1cm4gTWF0aC5mbG9vcih0aW1lc3RhbXAgKyBtaWxsaXNlY29uZHMgLyAxMDAwKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgdGltZSA9PT0gJ251bWJlcicpIHtcbiAgICByZXR1cm4gdGltZXN0YW1wICsgdGltZTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm47XG4gIH1cblxufTsiXSwibmFtZXMiOlsibXMiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyIsInRpbWUiLCJpYXQiLCJ0aW1lc3RhbXAiLCJNYXRoIiwiZmxvb3IiLCJEYXRlIiwibm93IiwibWlsbGlzZWNvbmRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js":
/*!****************************************************************!*\
  !*** ./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst ASYMMETRIC_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./asymmetricKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/asymmetricKeyDetailsSupported.js\");\nconst RSA_PSS_KEY_DETAILS_SUPPORTED = __webpack_require__(/*! ./rsaPssKeyDetailsSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/rsaPssKeyDetailsSupported.js\");\nconst allowedAlgorithmsForKeys = {\n    \"ec\": [\n        \"ES256\",\n        \"ES384\",\n        \"ES512\"\n    ],\n    \"rsa\": [\n        \"RS256\",\n        \"PS256\",\n        \"RS384\",\n        \"PS384\",\n        \"RS512\",\n        \"PS512\"\n    ],\n    \"rsa-pss\": [\n        \"PS256\",\n        \"PS384\",\n        \"PS512\"\n    ]\n};\nconst allowedCurves = {\n    ES256: \"prime256v1\",\n    ES384: \"secp384r1\",\n    ES512: \"secp521r1\"\n};\nmodule.exports = function(algorithm, key) {\n    if (!algorithm || !key) return;\n    const keyType = key.asymmetricKeyType;\n    if (!keyType) return;\n    const allowedAlgorithms = allowedAlgorithmsForKeys[keyType];\n    if (!allowedAlgorithms) {\n        throw new Error(`Unknown key type \"${keyType}\".`);\n    }\n    if (!allowedAlgorithms.includes(algorithm)) {\n        throw new Error(`\"alg\" parameter for \"${keyType}\" key type must be one of: ${allowedAlgorithms.join(\", \")}.`);\n    }\n    /*\n   * Ignore the next block from test coverage because it gets executed\n   * conditionally depending on the Node version. Not ignoring it would\n   * prevent us from reaching the target % of coverage for versions of\n   * Node under 15.7.0.\n   */ /* istanbul ignore next */ if (ASYMMETRIC_KEY_DETAILS_SUPPORTED) {\n        switch(keyType){\n            case \"ec\":\n                const keyCurve = key.asymmetricKeyDetails.namedCurve;\n                const allowedCurve = allowedCurves[algorithm];\n                if (keyCurve !== allowedCurve) {\n                    throw new Error(`\"alg\" parameter \"${algorithm}\" requires curve \"${allowedCurve}\".`);\n                }\n                break;\n            case \"rsa-pss\":\n                if (RSA_PSS_KEY_DETAILS_SUPPORTED) {\n                    const length = parseInt(algorithm.slice(-3), 10);\n                    const { hashAlgorithm, mgf1HashAlgorithm, saltLength } = key.asymmetricKeyDetails;\n                    if (hashAlgorithm !== `sha${length}` || mgf1HashAlgorithm !== hashAlgorithm) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                    if (saltLength !== undefined && saltLength > length >> 3) {\n                        throw new Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of \"alg\" ${algorithm}.`);\n                    }\n                }\n                break;\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/sign.js":
/*!*******************************************!*\
  !*** ./node_modules/jsonwebtoken/sign.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst includes = __webpack_require__(/*! lodash.includes */ \"(rsc)/./node_modules/lodash.includes/index.js\");\nconst isBoolean = __webpack_require__(/*! lodash.isboolean */ \"(rsc)/./node_modules/lodash.isboolean/index.js\");\nconst isInteger = __webpack_require__(/*! lodash.isinteger */ \"(rsc)/./node_modules/lodash.isinteger/index.js\");\nconst isNumber = __webpack_require__(/*! lodash.isnumber */ \"(rsc)/./node_modules/lodash.isnumber/index.js\");\nconst isPlainObject = __webpack_require__(/*! lodash.isplainobject */ \"(rsc)/./node_modules/lodash.isplainobject/index.js\");\nconst isString = __webpack_require__(/*! lodash.isstring */ \"(rsc)/./node_modules/lodash.isstring/index.js\");\nconst once = __webpack_require__(/*! lodash.once */ \"(rsc)/./node_modules/lodash.once/index.js\");\nconst { KeyObject, createSecretKey, createPrivateKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst SUPPORTED_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\",\n    \"ES256\",\n    \"ES384\",\n    \"ES512\",\n    \"HS256\",\n    \"HS384\",\n    \"HS512\",\n    \"none\"\n];\nif (PS_SUPPORTED) {\n    SUPPORTED_ALGS.splice(3, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nconst sign_options_schema = {\n    expiresIn: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"expiresIn\" should be a number of seconds or string representing a timespan'\n    },\n    notBefore: {\n        isValid: function(value) {\n            return isInteger(value) || isString(value) && value;\n        },\n        message: '\"notBefore\" should be a number of seconds or string representing a timespan'\n    },\n    audience: {\n        isValid: function(value) {\n            return isString(value) || Array.isArray(value);\n        },\n        message: '\"audience\" must be a string or array'\n    },\n    algorithm: {\n        isValid: includes.bind(null, SUPPORTED_ALGS),\n        message: '\"algorithm\" must be a valid string enum value'\n    },\n    header: {\n        isValid: isPlainObject,\n        message: '\"header\" must be an object'\n    },\n    encoding: {\n        isValid: isString,\n        message: '\"encoding\" must be a string'\n    },\n    issuer: {\n        isValid: isString,\n        message: '\"issuer\" must be a string'\n    },\n    subject: {\n        isValid: isString,\n        message: '\"subject\" must be a string'\n    },\n    jwtid: {\n        isValid: isString,\n        message: '\"jwtid\" must be a string'\n    },\n    noTimestamp: {\n        isValid: isBoolean,\n        message: '\"noTimestamp\" must be a boolean'\n    },\n    keyid: {\n        isValid: isString,\n        message: '\"keyid\" must be a string'\n    },\n    mutatePayload: {\n        isValid: isBoolean,\n        message: '\"mutatePayload\" must be a boolean'\n    },\n    allowInsecureKeySizes: {\n        isValid: isBoolean,\n        message: '\"allowInsecureKeySizes\" must be a boolean'\n    },\n    allowInvalidAsymmetricKeyTypes: {\n        isValid: isBoolean,\n        message: '\"allowInvalidAsymmetricKeyTypes\" must be a boolean'\n    }\n};\nconst registered_claims_schema = {\n    iat: {\n        isValid: isNumber,\n        message: '\"iat\" should be a number of seconds'\n    },\n    exp: {\n        isValid: isNumber,\n        message: '\"exp\" should be a number of seconds'\n    },\n    nbf: {\n        isValid: isNumber,\n        message: '\"nbf\" should be a number of seconds'\n    }\n};\nfunction validate(schema, allowUnknown, object, parameterName) {\n    if (!isPlainObject(object)) {\n        throw new Error('Expected \"' + parameterName + '\" to be a plain object.');\n    }\n    Object.keys(object).forEach(function(key) {\n        const validator = schema[key];\n        if (!validator) {\n            if (!allowUnknown) {\n                throw new Error('\"' + key + '\" is not allowed in \"' + parameterName + '\"');\n            }\n            return;\n        }\n        if (!validator.isValid(object[key])) {\n            throw new Error(validator.message);\n        }\n    });\n}\nfunction validateOptions(options) {\n    return validate(sign_options_schema, false, options, \"options\");\n}\nfunction validatePayload(payload) {\n    return validate(registered_claims_schema, true, payload, \"payload\");\n}\nconst options_to_payload = {\n    \"audience\": \"aud\",\n    \"issuer\": \"iss\",\n    \"subject\": \"sub\",\n    \"jwtid\": \"jti\"\n};\nconst options_for_objects = [\n    \"expiresIn\",\n    \"notBefore\",\n    \"noTimestamp\",\n    \"audience\",\n    \"issuer\",\n    \"subject\",\n    \"jwtid\"\n];\nmodule.exports = function(payload, secretOrPrivateKey, options, callback) {\n    if (typeof options === \"function\") {\n        callback = options;\n        options = {};\n    } else {\n        options = options || {};\n    }\n    const isObjectPayload = typeof payload === \"object\" && !Buffer.isBuffer(payload);\n    const header = Object.assign({\n        alg: options.algorithm || \"HS256\",\n        typ: isObjectPayload ? \"JWT\" : undefined,\n        kid: options.keyid\n    }, options.header);\n    function failure(err) {\n        if (callback) {\n            return callback(err);\n        }\n        throw err;\n    }\n    if (!secretOrPrivateKey && options.algorithm !== \"none\") {\n        return failure(new Error(\"secretOrPrivateKey must have a value\"));\n    }\n    if (secretOrPrivateKey != null && !(secretOrPrivateKey instanceof KeyObject)) {\n        try {\n            secretOrPrivateKey = createPrivateKey(secretOrPrivateKey);\n        } catch (_) {\n            try {\n                secretOrPrivateKey = createSecretKey(typeof secretOrPrivateKey === \"string\" ? Buffer.from(secretOrPrivateKey) : secretOrPrivateKey);\n            } catch (_) {\n                return failure(new Error(\"secretOrPrivateKey is not valid key material\"));\n            }\n        }\n    }\n    if (header.alg.startsWith(\"HS\") && secretOrPrivateKey.type !== \"secret\") {\n        return failure(new Error(`secretOrPrivateKey must be a symmetric key when using ${header.alg}`));\n    } else if (/^(?:RS|PS|ES)/.test(header.alg)) {\n        if (secretOrPrivateKey.type !== \"private\") {\n            return failure(new Error(`secretOrPrivateKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInsecureKeySizes && !header.alg.startsWith(\"ES\") && secretOrPrivateKey.asymmetricKeyDetails !== undefined && //KeyObject.asymmetricKeyDetails is supported in Node 15+\n        secretOrPrivateKey.asymmetricKeyDetails.modulusLength < 2048) {\n            return failure(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n        }\n    }\n    if (typeof payload === \"undefined\") {\n        return failure(new Error(\"payload is required\"));\n    } else if (isObjectPayload) {\n        try {\n            validatePayload(payload);\n        } catch (error) {\n            return failure(error);\n        }\n        if (!options.mutatePayload) {\n            payload = Object.assign({}, payload);\n        }\n    } else {\n        const invalid_options = options_for_objects.filter(function(opt) {\n            return typeof options[opt] !== \"undefined\";\n        });\n        if (invalid_options.length > 0) {\n            return failure(new Error(\"invalid \" + invalid_options.join(\",\") + \" option for \" + typeof payload + \" payload\"));\n        }\n    }\n    if (typeof payload.exp !== \"undefined\" && typeof options.expiresIn !== \"undefined\") {\n        return failure(new Error('Bad \"options.expiresIn\" option the payload already has an \"exp\" property.'));\n    }\n    if (typeof payload.nbf !== \"undefined\" && typeof options.notBefore !== \"undefined\") {\n        return failure(new Error('Bad \"options.notBefore\" option the payload already has an \"nbf\" property.'));\n    }\n    try {\n        validateOptions(options);\n    } catch (error) {\n        return failure(error);\n    }\n    if (!options.allowInvalidAsymmetricKeyTypes) {\n        try {\n            validateAsymmetricKey(header.alg, secretOrPrivateKey);\n        } catch (error) {\n            return failure(error);\n        }\n    }\n    const timestamp = payload.iat || Math.floor(Date.now() / 1000);\n    if (options.noTimestamp) {\n        delete payload.iat;\n    } else if (isObjectPayload) {\n        payload.iat = timestamp;\n    }\n    if (typeof options.notBefore !== \"undefined\") {\n        try {\n            payload.nbf = timespan(options.notBefore, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.nbf === \"undefined\") {\n            return failure(new Error('\"notBefore\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    if (typeof options.expiresIn !== \"undefined\" && typeof payload === \"object\") {\n        try {\n            payload.exp = timespan(options.expiresIn, timestamp);\n        } catch (err) {\n            return failure(err);\n        }\n        if (typeof payload.exp === \"undefined\") {\n            return failure(new Error('\"expiresIn\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n        }\n    }\n    Object.keys(options_to_payload).forEach(function(key) {\n        const claim = options_to_payload[key];\n        if (typeof options[key] !== \"undefined\") {\n            if (typeof payload[claim] !== \"undefined\") {\n                return failure(new Error('Bad \"options.' + key + '\" option. The payload already has an \"' + claim + '\" property.'));\n            }\n            payload[claim] = options[key];\n        }\n    });\n    const encoding = options.encoding || \"utf8\";\n    if (typeof callback === \"function\") {\n        callback = callback && once(callback);\n        jws.createSign({\n            header: header,\n            privateKey: secretOrPrivateKey,\n            payload: payload,\n            encoding: encoding\n        }).once(\"error\", callback).once(\"done\", function(signature) {\n            // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n            if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n                return callback(new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`));\n            }\n            callback(null, signature);\n        });\n    } else {\n        let signature = jws.sign({\n            header: header,\n            payload: payload,\n            secret: secretOrPrivateKey,\n            encoding: encoding\n        });\n        // TODO: Remove in favor of the modulus length check before signing once node 15+ is the minimum supported version\n        if (!options.allowInsecureKeySizes && /^(?:RS|PS)/.test(header.alg) && signature.length < 256) {\n            throw new Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${header.alg}`);\n        }\n        return signature;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/sign.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/jsonwebtoken/verify.js":
/*!*********************************************!*\
  !*** ./node_modules/jsonwebtoken/verify.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst JsonWebTokenError = __webpack_require__(/*! ./lib/JsonWebTokenError */ \"(rsc)/./node_modules/jsonwebtoken/lib/JsonWebTokenError.js\");\nconst NotBeforeError = __webpack_require__(/*! ./lib/NotBeforeError */ \"(rsc)/./node_modules/jsonwebtoken/lib/NotBeforeError.js\");\nconst TokenExpiredError = __webpack_require__(/*! ./lib/TokenExpiredError */ \"(rsc)/./node_modules/jsonwebtoken/lib/TokenExpiredError.js\");\nconst decode = __webpack_require__(/*! ./decode */ \"(rsc)/./node_modules/jsonwebtoken/decode.js\");\nconst timespan = __webpack_require__(/*! ./lib/timespan */ \"(rsc)/./node_modules/jsonwebtoken/lib/timespan.js\");\nconst validateAsymmetricKey = __webpack_require__(/*! ./lib/validateAsymmetricKey */ \"(rsc)/./node_modules/jsonwebtoken/lib/validateAsymmetricKey.js\");\nconst PS_SUPPORTED = __webpack_require__(/*! ./lib/psSupported */ \"(rsc)/./node_modules/jsonwebtoken/lib/psSupported.js\");\nconst jws = __webpack_require__(/*! jws */ \"(rsc)/./node_modules/jws/index.js\");\nconst { KeyObject, createSecretKey, createPublicKey } = __webpack_require__(/*! crypto */ \"crypto\");\nconst PUB_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst EC_KEY_ALGS = [\n    \"ES256\",\n    \"ES384\",\n    \"ES512\"\n];\nconst RSA_KEY_ALGS = [\n    \"RS256\",\n    \"RS384\",\n    \"RS512\"\n];\nconst HS_ALGS = [\n    \"HS256\",\n    \"HS384\",\n    \"HS512\"\n];\nif (PS_SUPPORTED) {\n    PUB_KEY_ALGS.splice(PUB_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n    RSA_KEY_ALGS.splice(RSA_KEY_ALGS.length, 0, \"PS256\", \"PS384\", \"PS512\");\n}\nmodule.exports = function(jwtString, secretOrPublicKey, options, callback) {\n    if (typeof options === \"function\" && !callback) {\n        callback = options;\n        options = {};\n    }\n    if (!options) {\n        options = {};\n    }\n    //clone this object since we are going to mutate it.\n    options = Object.assign({}, options);\n    let done;\n    if (callback) {\n        done = callback;\n    } else {\n        done = function(err, data) {\n            if (err) throw err;\n            return data;\n        };\n    }\n    if (options.clockTimestamp && typeof options.clockTimestamp !== \"number\") {\n        return done(new JsonWebTokenError(\"clockTimestamp must be a number\"));\n    }\n    if (options.nonce !== undefined && (typeof options.nonce !== \"string\" || options.nonce.trim() === \"\")) {\n        return done(new JsonWebTokenError(\"nonce must be a non-empty string\"));\n    }\n    if (options.allowInvalidAsymmetricKeyTypes !== undefined && typeof options.allowInvalidAsymmetricKeyTypes !== \"boolean\") {\n        return done(new JsonWebTokenError(\"allowInvalidAsymmetricKeyTypes must be a boolean\"));\n    }\n    const clockTimestamp = options.clockTimestamp || Math.floor(Date.now() / 1000);\n    if (!jwtString) {\n        return done(new JsonWebTokenError(\"jwt must be provided\"));\n    }\n    if (typeof jwtString !== \"string\") {\n        return done(new JsonWebTokenError(\"jwt must be a string\"));\n    }\n    const parts = jwtString.split(\".\");\n    if (parts.length !== 3) {\n        return done(new JsonWebTokenError(\"jwt malformed\"));\n    }\n    let decodedToken;\n    try {\n        decodedToken = decode(jwtString, {\n            complete: true\n        });\n    } catch (err) {\n        return done(err);\n    }\n    if (!decodedToken) {\n        return done(new JsonWebTokenError(\"invalid token\"));\n    }\n    const header = decodedToken.header;\n    let getSecret;\n    if (typeof secretOrPublicKey === \"function\") {\n        if (!callback) {\n            return done(new JsonWebTokenError(\"verify must be called asynchronous if secret or public key is provided as a callback\"));\n        }\n        getSecret = secretOrPublicKey;\n    } else {\n        getSecret = function(header, secretCallback) {\n            return secretCallback(null, secretOrPublicKey);\n        };\n    }\n    return getSecret(header, function(err, secretOrPublicKey) {\n        if (err) {\n            return done(new JsonWebTokenError(\"error in secret or public key callback: \" + err.message));\n        }\n        const hasSignature = parts[2].trim() !== \"\";\n        if (!hasSignature && secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"jwt signature is required\"));\n        }\n        if (hasSignature && !secretOrPublicKey) {\n            return done(new JsonWebTokenError(\"secret or public key must be provided\"));\n        }\n        if (!hasSignature && !options.algorithms) {\n            return done(new JsonWebTokenError('please specify \"none\" in \"algorithms\" to verify unsigned tokens'));\n        }\n        if (secretOrPublicKey != null && !(secretOrPublicKey instanceof KeyObject)) {\n            try {\n                secretOrPublicKey = createPublicKey(secretOrPublicKey);\n            } catch (_) {\n                try {\n                    secretOrPublicKey = createSecretKey(typeof secretOrPublicKey === \"string\" ? Buffer.from(secretOrPublicKey) : secretOrPublicKey);\n                } catch (_) {\n                    return done(new JsonWebTokenError(\"secretOrPublicKey is not valid key material\"));\n                }\n            }\n        }\n        if (!options.algorithms) {\n            if (secretOrPublicKey.type === \"secret\") {\n                options.algorithms = HS_ALGS;\n            } else if ([\n                \"rsa\",\n                \"rsa-pss\"\n            ].includes(secretOrPublicKey.asymmetricKeyType)) {\n                options.algorithms = RSA_KEY_ALGS;\n            } else if (secretOrPublicKey.asymmetricKeyType === \"ec\") {\n                options.algorithms = EC_KEY_ALGS;\n            } else {\n                options.algorithms = PUB_KEY_ALGS;\n            }\n        }\n        if (options.algorithms.indexOf(decodedToken.header.alg) === -1) {\n            return done(new JsonWebTokenError(\"invalid algorithm\"));\n        }\n        if (header.alg.startsWith(\"HS\") && secretOrPublicKey.type !== \"secret\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be a symmetric key when using ${header.alg}`));\n        } else if (/^(?:RS|PS|ES)/.test(header.alg) && secretOrPublicKey.type !== \"public\") {\n            return done(new JsonWebTokenError(`secretOrPublicKey must be an asymmetric key when using ${header.alg}`));\n        }\n        if (!options.allowInvalidAsymmetricKeyTypes) {\n            try {\n                validateAsymmetricKey(header.alg, secretOrPublicKey);\n            } catch (e) {\n                return done(e);\n            }\n        }\n        let valid;\n        try {\n            valid = jws.verify(jwtString, decodedToken.header.alg, secretOrPublicKey);\n        } catch (e) {\n            return done(e);\n        }\n        if (!valid) {\n            return done(new JsonWebTokenError(\"invalid signature\"));\n        }\n        const payload = decodedToken.payload;\n        if (typeof payload.nbf !== \"undefined\" && !options.ignoreNotBefore) {\n            if (typeof payload.nbf !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid nbf value\"));\n            }\n            if (payload.nbf > clockTimestamp + (options.clockTolerance || 0)) {\n                return done(new NotBeforeError(\"jwt not active\", new Date(payload.nbf * 1000)));\n            }\n        }\n        if (typeof payload.exp !== \"undefined\" && !options.ignoreExpiration) {\n            if (typeof payload.exp !== \"number\") {\n                return done(new JsonWebTokenError(\"invalid exp value\"));\n            }\n            if (clockTimestamp >= payload.exp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"jwt expired\", new Date(payload.exp * 1000)));\n            }\n        }\n        if (options.audience) {\n            const audiences = Array.isArray(options.audience) ? options.audience : [\n                options.audience\n            ];\n            const target = Array.isArray(payload.aud) ? payload.aud : [\n                payload.aud\n            ];\n            const match = target.some(function(targetAudience) {\n                return audiences.some(function(audience) {\n                    return audience instanceof RegExp ? audience.test(targetAudience) : audience === targetAudience;\n                });\n            });\n            if (!match) {\n                return done(new JsonWebTokenError(\"jwt audience invalid. expected: \" + audiences.join(\" or \")));\n            }\n        }\n        if (options.issuer) {\n            const invalid_issuer = typeof options.issuer === \"string\" && payload.iss !== options.issuer || Array.isArray(options.issuer) && options.issuer.indexOf(payload.iss) === -1;\n            if (invalid_issuer) {\n                return done(new JsonWebTokenError(\"jwt issuer invalid. expected: \" + options.issuer));\n            }\n        }\n        if (options.subject) {\n            if (payload.sub !== options.subject) {\n                return done(new JsonWebTokenError(\"jwt subject invalid. expected: \" + options.subject));\n            }\n        }\n        if (options.jwtid) {\n            if (payload.jti !== options.jwtid) {\n                return done(new JsonWebTokenError(\"jwt jwtid invalid. expected: \" + options.jwtid));\n            }\n        }\n        if (options.nonce) {\n            if (payload.nonce !== options.nonce) {\n                return done(new JsonWebTokenError(\"jwt nonce invalid. expected: \" + options.nonce));\n            }\n        }\n        if (options.maxAge) {\n            if (typeof payload.iat !== \"number\") {\n                return done(new JsonWebTokenError(\"iat required when maxAge is specified\"));\n            }\n            const maxAgeTimestamp = timespan(options.maxAge, payload.iat);\n            if (typeof maxAgeTimestamp === \"undefined\") {\n                return done(new JsonWebTokenError('\"maxAge\" should be a number of seconds or string representing a timespan eg: \"1d\", \"20h\", 60'));\n            }\n            if (clockTimestamp >= maxAgeTimestamp + (options.clockTolerance || 0)) {\n                return done(new TokenExpiredError(\"maxAge exceeded\", new Date(maxAgeTimestamp * 1000)));\n            }\n        }\n        if (options.complete === true) {\n            const signature = decodedToken.signature;\n            return done(null, {\n                header: header,\n                payload: payload,\n                signature: signature\n            });\n        }\n        return done(null, payload);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/jsonwebtoken/verify.js\n");

/***/ })

};
;