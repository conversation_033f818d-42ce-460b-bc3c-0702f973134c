import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getAuthManager } from '../../../../lib/auth';

export async function GET(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication and admin role
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    if (session.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Return available addons/features
    const addons = [
      {
        id: 'premium_support',
        name: 'Premium Support',
        description: 'Priority customer support with faster response times'
      },
      {
        id: 'bulk_operations',
        name: 'Bulk Operations',
        description: 'Process multiple items simultaneously'
      },
      {
        id: 'custom_themes',
        name: 'Custom Themes',
        description: 'Personalize the interface with custom themes'
      }
    ];

    return NextResponse.json({
      success: true,
      addons
    });

  } catch (error) {
    console.error('Get addons error:', error);
    return NextResponse.json(
      { success: false, error: error.message },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}