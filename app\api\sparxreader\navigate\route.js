import { NextResponse } from 'next/server';
import path from 'path';
import { getGlobalPage, getGlobalBrowser, setGlobalBrowser } from '../browser-context.js';
import { updateRealtimeData } from '../realtime/route.js';

export async function POST(request) {
  try {
    const { action, bookTitle, targetSrp } = await request.json();
    
    if (action === 'confirm') {
      const page = getGlobalPage();
      const browser = getGlobalBrowser();
      
      console.log('Navigate route - checking browser session:');
      console.log('Page exists:', !!page);
      console.log('Browser exists:', !!browser);
      
      if (!page) {
        console.log('No page found in global context');
        return NextResponse.json({ 
          success: false, 
          error: 'No browser session available. Please restart the application.'
        }, { status: 400 });
      }
      
      if (!browser) {
        console.log('No browser found in global context');
        return NextResponse.json({ 
          success: false, 
          error: 'No browser session available. Please restart the application.'
        }, { status: 400 });
      }
      
      console.log('Browser session found, proceeding...');
      
      console.log('User confirmed book, extracting story content from current page...');
      
      // Extract the actual book title from the book page
      let actualBookTitle = bookTitle; // fallback to passed title
      try {
        const bookTitleElement = await page.waitForSelector('h2.sr_942936b5.sr_b59a8fb2', { timeout: 5000 });
        if (bookTitleElement) {
          actualBookTitle = await bookTitleElement.textContent();
          actualBookTitle = actualBookTitle.trim();
          console.log(`Extracted actual book title: ${actualBookTitle}`);
        }
      } catch (error) {
        console.log('Could not extract book title from book page, using fallback');
      }
      
      //  just extract the content directly since its the second go around
      // Wait a moment to ensure page is fully loaded
      await page.waitForTimeout(1000);
      
      // Extract the story content from the current page,
      const storyContent = await page.evaluate(() => {
        const fullText = document.body.innerText;
        
        // Find the start and end markers
        const startMarker = "Start reading here";
        const endMarker = "Stop reading here";
        
        const startIndex = fullText.indexOf(startMarker);
        const endIndex = fullText.indexOf(endMarker);
        
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          // Extract content between the markers (excluding the markers themselves)
          return fullText.substring(startIndex + startMarker.length, endIndex).trim();
        } else if (startIndex !== -1) {
          // If only start marker found, extract from start marker to end
          return fullText.substring(startIndex + startMarker.length).trim();
        } else {
          // If no markers found, return full text as fallback
          return fullText;
        }
      });
      
      // Log some of the story content for debugging
      const storyPreview = storyContent.substring(0, 500);
      console.log('Story content extracted (first 500 characters):');
      console.log(storyPreview);
      console.log(`Total story length: ${storyContent.length} characters`);
      
      // Store the story content in the session for use with AI
      global.sessionStoryContent = storyContent;
      console.log('Story content stored for AI context');
      
      // Store target SRP in session info and browser localStorage
      if (targetSrp) {
        global.sessionSrpInfo = global.sessionSrpInfo || {};
        global.sessionSrpInfo.targetSrpNeeded = targetSrp;
        console.log(`Target SRP set to: ${targetSrp}`);
        
        // Store target SRP in browser localStorage so extension can access it
        await page.evaluate((target) => {
          localStorage.setItem('targetSrp', target.toString());
        }, targetSrp);
      }
      
      // Get initial SRP count before starting questions
      const initialSrpInfo = global.sessionSrpInfo || {};
      const initialUserTotalSrp = await page.evaluate(() => {
        const userTotalSrpElement = document.querySelector('.sr_92b39de6');
        return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\d,]/g, '').replace(',', '') : null;
      }).catch(() => null);
      
      console.log(`Initial User Total SRP: ${initialUserTotalSrp}`);
      
      // Now click "I have read up to here" button
      try {
        console.log('Looking for "I have read up to here" button...');
        
        // Try multiple selectors for the "I have read up to here" button
    const readUpToHereSelectors = [
      'xpath=//*[@id="book-scroll"]/div/div/div/button[2]', // Primary XPath first
      '#book-scroll div:nth-child(2) div:nth-child(3) div div div button',
      'button:has-text("I have read up to here")',
      'button:has-text("read up to here")',
      '[data-test-id*="read-up-to-here"]',
      'button[class*="read"]'
    ];
        
        let readUpToHereClicked = false;
        for (const selector of readUpToHereSelectors) {
          try {
            await page.click(selector, { timeout: 3000 });
            console.log(`Clicked "I have read up to here" button with selector: ${selector}`);
            readUpToHereClicked = true;
            break;
          } catch (error) {
            // Continue to next selector
          }
        }
        
        if (readUpToHereClicked) {
          // Wait for the page to respond
          await page.waitForTimeout(2000);
          
          // Check if we have the "Did you read carefully?" dialog or direct "Start" button
          const pageText = await page.textContent('body');
          
          if (pageText.includes('Did you read carefully?')) {
            console.log('Found "Did you read carefully?" dialog');
            
            // Click "Yes, ask me the questions" button
            try {
              // Try multiple selectors for "Yes" button with increased timeout
              const yesSelectors = [
                'xpath=//*[@id="book-scroll"]/div/div/div/button[2]', // main but sparx may change so if so change ts twin
                'button:has-text("Yes, ask me the questions")', // Text-based fallback
                '#book-scroll div div div button:nth-child(2)', // useless
                'button:has-text("Yes") >> nth=1' // General Yes button fallback
              ];
              
              let yesClicked = false;
              for (const selector of yesSelectors) {
                try {
                  await page.click(selector, { timeout: 5000 });
                  console.log(`Clicked "Yes" button with selector: ${selector}`);
                  yesClicked = true;
                  break;
                } catch (error) {
                  console.log(`Failed to click with selector ${selector}:`, error.message);
                }
              }
              
              if (!yesClicked) {
                throw new Error('Could not find "Yes, ask me the questions" button');
              }
              console.log('Clicked "Yes, ask me the questions" button');
              
              // Wait for questions to load
              await page.waitForTimeout(3000);
              
              // Start the question-solving loop
              console.log('Starting question-solving process...');
              
              // Initialize real-time data - doesnt work idk 
              updateRealtimeData({
                isRunning: true,
                currentQuestion: '',
                currentAnswer: '',
                questionNumber: 0,
                srpEarned: 0,
                questionHistory: [],
                progress: 0,
                status: 'starting'
              });
              
              await solveQuestions(page, initialUserTotalSrp, targetSrp);
              
            } catch (error) {
              console.log('Could not find "Yes, ask me the questions" button:', error.message);
            }
          } else {
            // Look for direct "Start" button
            console.log('Looking for direct "Start" button...');
            
            const startSelectors = [
              '#book-scroll div div div button',
              'button:has-text("Start")',
              'button:has-text("start")',
              '[data-test-id*="start"]',
              'button[class*="start"]'
            ];
            
            let startClicked = false;
            for (const selector of startSelectors) {
              try {
                await page.click(selector, { timeout: 3000 });
                console.log(`Clicked "Start" button with selector: ${selector}`);
                startClicked = true;
                break;
              } catch (error) {
                // Continue to next selector
              }
            }
            
            if (startClicked) {
              // Wait for questions to load
              await page.waitForTimeout(3000);
              
              // Start the question-solving loop  
              console.log('Starting question-solving process...');
              
              // Initialize real-time data
              updateRealtimeData({
                isRunning: true,
                currentQuestion: '',
                currentAnswer: '',
                questionNumber: 0,
                srpEarned: 0,
                questionHistory: [],
                progress: 0,
                status: 'starting'
              });
              
              await solveQuestions(page, initialUserTotalSrp, targetSrp);
            } else {
              console.log('Could not find "Start" button - checking if questions already appeared');
              
              // Check if questions are already visible
              const hasQuestions = await page.evaluate(() => {
                return document.querySelector('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span') !== null;
              });
              
              if (hasQuestions) {
                console.log('Questions found - starting solving process');
                
                // Initialize real-time data
                updateRealtimeData({
                  isRunning: true,
                  currentQuestion: '',
                  currentAnswer: '',
                  questionNumber: 0,
                  srpEarned: 0,
                  questionHistory: [],
                  progress: 0,
                  status: 'starting'
                });
                
                await solveQuestions(page, initialUserTotalSrp, targetSrp);
              } else {
                console.log('No questions found after timeout');
              }
            }
          }
        } else {
          console.log('Could not find "I have read up to here" button');
        }
      } catch (error) {
        console.log('Error in question flow:', error.message);
      }
      
      // Take a final screenshot
      const screenshotPath = path.resolve(process.cwd(), 'public', 'screenshot.png');
      await page.screenshot({ path: screenshotPath });
      
      return NextResponse.json({ 
        success: true, 
        message: 'Successfully extracted story content',
        bookTitle: actualBookTitle,
        storyContent: storyContent,
        screenshot: '/screenshot.png'
      });
    } else {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid action'
      }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in navigate endpoint:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

// Function to solve questions using AI
async function solveQuestions(page, initialUserTotalSrp, srpTarget = 100) {
  try {
    let questionCount = 0;
    let lastQuestionNumber = '';
    const maxQuestions = Math.max(10, Math.ceil(srpTarget / 10)); // Dynamic limit based on SRP target (roughly 10 SRP per question)
    
    while (questionCount < maxQuestions) {
      console.log(`Processing question ${questionCount + 1}...`);
      
      // Wait for question to load
      await page.waitForTimeout(2000);
      
      // Extract the question text and answer options using the specific selectors
      const questionData = await page.evaluate(() => {
        // Get the question number from the span
        const questionNumberElement = document.querySelector('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span');
        
        // Get the question text from the div
        const questionTextElement = document.querySelector('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > div');
        
        // Get all answer option buttons
        const answerButtons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
        
        if (questionNumberElement && questionTextElement) {
          const questionNumber = questionNumberElement.textContent.trim();
          const questionText = questionTextElement.textContent.trim();
          
          // Extract answer options
          const answerOptions = [];
          answerButtons.forEach((button, index) => {
            const buttonText = button.textContent.trim();
            if (buttonText) {
              answerOptions.push(`${index + 1}. ${buttonText}`);
            }
          });
          
          // Check if this is a valid question (has Q followed by number and dot)
          if (/Q\d+\./.test(questionNumber)) {
            return {
              found: true,
              questionNumber: questionNumber,
              questionText: questionText,
              answerOptions: answerOptions,
              fullQuestion: questionNumber + ' ' + questionText
            };
          }
        }
        
        return { found: false, questionText: '', questionNumber: '', fullQuestion: '', answerOptions: [] };
      });
      
      if (!questionData.found) {
        console.log('No more questions found, ending question-solving process');
        break;
      }
      
      // Check if this is the same question as before (to avoid infinite loops)
      if (questionData.questionNumber === lastQuestionNumber) {
        console.log('Same question detected, might be stuck. Ending process.');
        break;
      }
      
      lastQuestionNumber = questionData.questionNumber;
      console.log('Question found:', questionData.questionNumber, '-', questionData.questionText.substring(0, 200) + '...');
      console.log('Answer options:', questionData.answerOptions);
      
      // Update real-time data with current question
      updateRealtimeData({
        isRunning: true,
        currentQuestion: questionData.questionText,
        currentAnswer: '',
        questionNumber: parseInt(questionData.questionNumber.replace(/\D/g, '')),
        status: 'solving'
      });
      
      // Send question to AI with story context and answer options
      const answer = await getAIAnswer(questionData.fullQuestion, questionData.answerOptions);
      console.log('AI Answer:', answer);
      
      // Update real-time data with AI answer
      updateRealtimeData({
        currentAnswer: answer,
        status: 'answering'
      });
      
      // Try to select the answer using the specific button selectors
      await selectAnswer(page, answer, questionData.questionNumber);
      
      // Add to question history and update real-time data
      const currentHistory = global.realtimeData?.questionHistory || [];
      const newHistoryItem = {
        number: parseInt(questionData.questionNumber.replace(/\D/g, '')),
        question: questionData.questionText,
        answer: answer
      };
      
      updateRealtimeData({
        questionHistory: [...currentHistory, newHistoryItem],
        status: 'completed'
      });
      
      // Wait a bit after selecting answer
      await page.waitForTimeout(2000);
      
      // Check if there's a "Next" button or similar to move to next question
      const nextButton = await page.$('button:has-text("Next")') || 
                        await page.$('button:has-text("Continue")') ||
                        await page.$('[data-test-id*="next"]') ||
                        await page.$('button[class*="next"]');
      
      if (nextButton) {
        await nextButton.click();
        console.log('Clicked next button');
        await page.waitForTimeout(1000);
      } else {
        console.log('No next button found, waiting to see if question changes automatically');
        await page.waitForTimeout(3000);
        
        // Check if the question number has changed
        const newQuestionData = await page.evaluate(() => {
          const questionNumberElement = document.querySelector('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > h2 > span');
          if (questionNumberElement) {
            return questionNumberElement.textContent.trim();
          }
          return '';
        });
        
        if (newQuestionData === lastQuestionNumber) {
          console.log('Question did not change, ending process');
          break;
        }
      }
      
      questionCount++;
    }
    
    console.log(`Question-solving process completed. Processed ${questionCount} questions.`);
    
    // Update real-time data - automation completed
    updateRealtimeData({
      isRunning: false,
      status: 'completed'
    });
    
    // Check SRP earned and restart if needed
      await checkSrpAndRestart(page, initialUserTotalSrp);
    
  } catch (error) {
    console.error('Error in question-solving process:', error);
  }
}

// Function to get AI answer using the story context
async function getAIAnswer(questionText, answerOptions) {
  try {
    const storyContent = global.sessionStoryContent || '';
    
    // Create prompt with answer options
    let prompt = `Here is the context from the story:\n${storyContent}\n\nNow, please answer this question based on the story above:\n${questionText}\n\n`;
    
    if (answerOptions && answerOptions.length > 0) {
      prompt += `You MUST choose from one of these options only:\n${answerOptions.join('\n')}\n\nRespond with ONLY the exact text of the correct option (without the number). Do not add any explanation.`;
    } else {
      prompt += `Give ONLY the direct answer without explanation.`;
    }
    
    // Use the same API as the extension
    const API_KEY = 'AIzaSyAdbFHKgcsOz9YweT0fZCwJbNODoEwSGzs';
    const API_ENDPOINT = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';
    
    const requestBody = {
      contents: [{
        parts: [
          { text: prompt }
        ]
      }],
      generationConfig: {
        temperature: 0.1,
        topK: 1,
        topP: 1,
        maxOutputTokens: 2048
      }
    };
    
    const response = await fetch(`${API_ENDPOINT}?key=${API_KEY}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error?.message || `HTTP error ${response.status}`);
    }
    
    if (!data.candidates || data.candidates.length === 0) {
      throw new Error('No solution generated');
    }
    
    return data.candidates[0].content.parts[0].text.trim();
    
  } catch (error) {
    console.error('Error getting AI answer:', error);
    return 'Error getting answer';
  }
}

// Function to select the answer on the page
async function selectAnswer(page, answer, questionNumber) {
  try {
    console.log('Attempting to select answer:', answer);
    
    // Wait for buttons to be stable
    await page.waitForTimeout(1000);
    
    // Get button count first
    const buttonCount = await page.evaluate(() => {
      const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
      return buttons.length;
    });
    
    console.log(`Found ${buttonCount} answer buttons`);
    
    if (buttonCount === 0) {
      console.log('No answer buttons found');
      return;
    }
    
    // Check each button to find the one that matches the answer
    for (let i = 0; i < buttonCount; i++) {
      try {
        // Re-query the button each time to avoid detached DOM issues
        const buttonText = await page.evaluate((index) => {
          const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
          return buttons[index] ? buttons[index].textContent : null;
        }, i);
        
        console.log(`Button ${i + 1} text:`, buttonText);
        
        // Check if this button contains the answer (exact match or partial match)
        if (buttonText && (
          buttonText.toLowerCase().trim() === answer.toLowerCase().trim() ||
          buttonText.toLowerCase().includes(answer.toLowerCase()) ||
          answer.toLowerCase().includes(buttonText.toLowerCase())
        )) {
          // Try multiple click methods to handle intercepting elements
          let clicked = false;
          
          // Method 1: Direct DOM click (most reliable)
          try {
            clicked = await page.evaluate((index) => {
              const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
              if (buttons[index]) {
                buttons[index].click();
                return true;
              }
              return false;
            }, i);
          } catch (error) {
            console.log(`DOM click failed for button ${i + 1}:`, error.message);
          }
          
          // Method 2: Force click if DOM click failed
          if (!clicked) {
            try {
              clicked = await page.evaluate((index) => {
                const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
                if (buttons[index]) {
                  // Remove any intercepting elements temporarily
                  const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*="enter-down"]');
                  interceptors.forEach(el => el.style.pointerEvents = 'none');
                  
                  // Trigger click event
                  const event = new MouseEvent('click', { bubbles: true, cancelable: true });
                  buttons[index].dispatchEvent(event);
                  
                  // Restore pointer events
                  setTimeout(() => {
                    interceptors.forEach(el => el.style.pointerEvents = '');
                  }, 100);
                  
                  return true;
                }
                return false;
              }, i);
            } catch (error) {
              console.log(`Force click failed for button ${i + 1}:`, error.message);
            }
          }
          
          if (clicked) {
            console.log(`Selected answer button ${i + 1}: ${buttonText}`);
            return;
          }
        }
      } catch (error) {
        console.log(`Error checking button ${i + 1}:`, error.message);
      }
    }
    
    // If no exact match found, try to find the best partial match
    let bestMatch = -1;
    let bestMatchScore = 0;
    
    for (let i = 0; i < buttonCount; i++) {
      try {
        const buttonText = await page.evaluate((index) => {
          const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
          return buttons[index] ? buttons[index].textContent : null;
        }, i);
        
        if (buttonText) {
          // (simple word matching)
          const answerWords = answer.toLowerCase().split(' ');
          const buttonWords = buttonText.toLowerCase().split(' ');
          let matchCount = 0;
          
          answerWords.forEach(word => {
            if (buttonWords.some(buttonWord => buttonWord.includes(word) || word.includes(buttonWord))) {
              matchCount++;
            }
          });
          
          const score = matchCount / Math.max(answerWords.length, buttonWords.length);
          if (score > bestMatchScore) {
            bestMatchScore = score;
            bestMatch = i;
          }
        }
      } catch (error) {
        console.log(`Error calculating match for button ${i + 1}:`, error.message);
      }
    }
    
    if (bestMatch >= 0 && bestMatchScore > 0.3) {
      // Try  clicking shit for the best match
      let clicked = false;
      let buttonText = '';
      
      try {
        const result = await page.evaluate((index) => {
          const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
          if (buttons[index]) {
            try {
              buttons[index].click();
              return { success: true, text: buttons[index].textContent };
            } catch (e) {
              // Force click if normal click fails
              const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*="enter-down"]');
              interceptors.forEach(el => el.style.pointerEvents = 'none');
              
              const event = new MouseEvent('click', { bubbles: true, cancelable: true });
              buttons[index].dispatchEvent(event);
              
              setTimeout(() => {
                interceptors.forEach(el => el.style.pointerEvents = '');
              }, 100);
              
              return { success: true, text: buttons[index].textContent };
            }
          }
          return { success: false, text: null };
        }, bestMatch);
        
        clicked = result.success;
        buttonText = result.text;
      } catch (error) {
        console.log(`Error clicking best match button:`, error.message);
      }
      
      if (clicked) {
        console.log(`Selected best match button ${bestMatch + 1}: ${buttonText} (score: ${bestMatchScore})`);
        return;
      }
    }
    
    // If no exact match found, try the first button as fallback
    if (buttonCount > 0) {
      console.log('No exact match found, clicking first button as fallback');
      
      let clicked = false;
      let buttonText = '';
      
      try {
        const result = await page.evaluate(() => {
          const buttons = document.querySelectorAll('#root > div > div:nth-child(2) > div > div:nth-child(2) > div > div > div:nth-child(1) > div > div > button');
          if (buttons[0]) {
            try {
              buttons[0].click();
              return { success: true, text: buttons[0].textContent };
            } catch (e) {
              // Force click if normal click fails
              const interceptors = document.querySelectorAll('.sr_6c1797d2, [class*="enter-down"]');
              interceptors.forEach(el => el.style.pointerEvents = 'none');
              
              const event = new MouseEvent('click', { bubbles: true, cancelable: true });
              buttons[0].dispatchEvent(event);
              
              setTimeout(() => {
                interceptors.forEach(el => el.style.pointerEvents = '');
              }, 100);
              
              return { success: true, text: buttons[0].textContent };
            }
          }
          return { success: false, text: null };
        });
        
        clicked = result.success;
        buttonText = result.text;
      } catch (error) {
        console.log(`Error clicking fallback button:`, error.message);
      }
      
      if (clicked) {
        console.log(`Selected first button: ${buttonText}`);
      }
    } else {
      console.log('Could not find any answer buttons');
    }
    
  } catch (error) {
    console.error('Error selecting answer:', error);
  }
}

// Function to check SRP earned and restart the flow if needed
async function checkSrpAndRestart(page, initialUserTotalSrp) {
  try {
    console.log('Checking SRP earned and determining next steps...');
    
    // Wait a moment for any final updates
    await page.waitForTimeout(2000);
    
    // Get current user total SRP
    const currentUserTotalSrp = await page.evaluate(() => {
      const userTotalSrpElement = document.querySelector('.sr_92b39de6');
      return userTotalSrpElement ? userTotalSrpElement.textContent.replace(/[^\d,]/g, '').replace(',', '') : null;
    }).catch(() => null);
    
    console.log(`Initial User Total SRP: ${initialUserTotalSrp}`);
    console.log(`Current User Total SRP: ${currentUserTotalSrp}`);
    
    // Calculate SRP earned
    let srpEarned = 0;
    if (initialUserTotalSrp && currentUserTotalSrp) {
      const initialNum = parseInt(initialUserTotalSrp) || 0;
      const currentNum = parseInt(currentUserTotalSrp) || 0;
      srpEarned = currentNum - initialNum;
    }
    
    console.log(`SRP Earned: ${srpEarned}`);

    // Update real-time SRP data
    updateRealtimeData({
      srpEarned: srpEarned
    });

    // Get the session SRP info
    const srpInfo = global.sessionSrpInfo || {};
    const initialSrp = parseInt(srpInfo.initialUserTotalSrp) || 0;
    const targetSrp = parseInt(srpInfo.targetSrpNeeded) || 0;
    const currentSrp = parseInt(currentUserTotalSrp) || 0;
    const totalSrpEarned = currentSrp - initialSrp;

    // Check if SRP target is reached
    if (targetSrp > 0 && totalSrpEarned >= targetSrp) {
      console.log(`🎯 SRP target reached! Earned ${totalSrpEarned} SRP (target was ${targetSrp})`);

      updateRealtimeData({
        isRunning: false,
        status: 'target_reached',
        srpEarned: totalSrpEarned
      });

      // Close browser after a short delay
      setTimeout(async () => {
        try {
          const browser = getGlobalBrowser();
          if (browser) {
            console.log('Auto-closing browser - SRP target reached');
            await browser.close();
            // Clear the global browser context
            const { clearGlobalBrowser } = require('../browser-context.js');
            clearGlobalBrowser();
          }
        } catch (error) {
          console.error('Error auto-closing browser:', error);
        }
      }, 3000);

      return; // Exit early, don't restart
    }
    
    console.log(`Initial User Total SRP: ${initialSrp}`);
    console.log(`Current User Total SRP: ${currentSrp}`);
    console.log(`SRP Earned This Session: ${totalSrpEarned}`);
    console.log(`Target SRP: ${targetSrp}`);
    
    // Update global session tracking
    global.sessionSrpGoal = totalSrpEarned;
    
    if (targetSrp > 0 && totalSrpEarned < targetSrp) {
      console.log(`Need more SRP (${totalSrpEarned}/${targetSrp}). Continuing with current book...`);
      
      // Extract story content again from current page
      const storyContent = await page.evaluate(() => {
        const fullText = document.body.innerText;
        const startMarker = "Start reading here";
        const endMarker = "Stop reading here";
        const startIndex = fullText.indexOf(startMarker);
        const endIndex = fullText.indexOf(endMarker);
        
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          return fullText.substring(startIndex + startMarker.length, endIndex).trim();
        } else if (startIndex !== -1) {
          return fullText.substring(startIndex + startMarker.length).trim();
        } else {
          return fullText;
        }
      });
      
      global.sessionStoryContent = storyContent;
      
      // Continue directly with question flow
      await continueQuestionFlow(page, initialUserTotalSrp);
    } else {
      console.log(`Target SRP reached! (${totalSrpEarned}/${targetSrp}). Session complete.`);
    }
    
  } catch (error) {
    console.error('Error checking SRP and restarting:', error);
  }
}

// Function to restart the book selection flow
async function restartBookFlow(page) {
  try {
    console.log('Restarting book selection flow...');
    
    // Navigate back to the library/book selection page
    // Try to find and click a back/home button
    const backSelectors = [
      'button:has-text("Back")',
      'button:has-text("Home")',
      'button:has-text("Library")',
      'a:has-text("Back")',
      'a:has-text("Home")',
      'a:has-text("Library")',
      '[data-test-id*="back"]',
      '[data-test-id*="home"]'
    ];
    
    let backClicked = false;
    for (const selector of backSelectors) {
      try {
        await page.click(selector, { timeout: 2000 });
        console.log(`Clicked back button with selector: ${selector}`);
        backClicked = true;
        break;
      } catch (error) {
        // Continue to next selector
      }
    }
    
    if (!backClicked) {
      
      console.log('No back button found, navigating to library URL...');
      await page.goto('https://www.sparxreader.com/library', { waitUntil: 'networkidle' });
    }
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // Look for a new book 
    console.log('Looking for next book...');
    
    // Extract new book info
    const newBookInfo = await page.evaluate(() => {
      const titleElement = document.querySelector('div.sr_ea851119');
      const srpNeededElement = document.querySelector('#root > div > div:nth-child(2) > div > div > div:nth-child(2) > div > div:nth-child(2) > div:nth-child(1) > div > div > div:nth-child(2) > div > div:nth-child(2)');
      
      return {
        bookTitle: titleElement ? titleElement.textContent.trim() : null,
        srpNeeded: srpNeededElement ? srpNeededElement.textContent.trim() : null
      };
    }).catch(() => ({ bookTitle: null, srpNeeded: null }));
    
    if (newBookInfo.bookTitle) {
      console.log(`Found new book: ${newBookInfo.bookTitle}`);
      console.log(`SRP Needed: ${newBookInfo.srpNeeded}`);
      
      // Update global session info
      global.sessionSrpInfo = newBookInfo;
      
      // Auto-confirm this book and continue 
      console.log('Auto-confirming new book and continuing...');
      
      // Extract story content and continue the flow
      await page.waitForTimeout(1000);
      
      const storyContent = await page.evaluate(() => {
        const fullText = document.body.innerText;
        
        const startMarker = "Start reading here";
        const endMarker = "Stop reading here";
        
        const startIndex = fullText.indexOf(startMarker);
        const endIndex = fullText.indexOf(endMarker);
        
        if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
          return fullText.substring(startIndex + startMarker.length, endIndex).trim();
        } else if (startIndex !== -1) {
          return fullText.substring(startIndex + startMarker.length).trim();
        } else {
          return fullText;
        }
      });
      
      // Store new story content
      global.sessionStoryContent = storyContent;
      console.log(`New story content extracted (${storyContent.length} characters)`);
      
      // Get new initial SRP count
      const newInitialSrp = await page.evaluate(() => {
        const totalSrpElement = document.querySelector('#header-portal > div:nth-child(3) > div:nth-child(1) > div');
        return totalSrpElement ? totalSrpElement.textContent.trim() : null;
      }).catch(() => null);
      
      // Continue with the question flow
      await continueQuestionFlow(page, newInitialSrp);
      
    } else {
      console.log('No new book found. Session may be complete or there might be an issue.');
    }
    
  } catch (error) {
    console.error('Error restarting book flow:', error);
  }
}

// Function to continue with the question flow for a new book
async function continueQuestionFlow(page, initialUserTotalSrp) {
  try {
    console.log('Continuing with question flow for new book...');
    
    // Click "I have read up to here" button and continue the flow
    const readUpToHereSelectors = [
      '#book-scroll div:nth-child(2) div:nth-child(3) div div div button',
      'button:has-text("I have read up to here")',
      'button:has-text("read up to here")',
      '[data-test-id*="read-up-to-here"]',
      'button[class*="read"]'
    ];
    
    let readUpToHereClicked = false;
    for (const selector of readUpToHereSelectors) {
      try {
        await page.click(selector, { timeout: 3000 });
        console.log(`Clicked "I have read up to here" button with selector: ${selector}`);
        readUpToHereClicked = true;
        break;
      } catch (error) {
        // Continue to next selector
      }
    }
    
    if (readUpToHereClicked) {
      await page.waitForTimeout(2000);
      
      // Check for dialog and handle accordingly
      const pageText = await page.textContent('body');
      
    if (pageText.includes('Did you read carefully?')) {
        console.log('Found "Did you read carefully?" dialog');
        const yesSelectors = [
          'xpath=//*[@id="book-scroll"]/div/div/div/button[2]', // Primary XPath
          'button:has-text("Yes, ask me the questions")', // Text-based fallback
          '#book-scroll div div div button:nth-child(2)', // Original selector 
          'button:has-text("Yes") >> nth=1' // General Yes button fallback
        ];
        
        let yesClicked = false;
        for (const selector of yesSelectors) {
          try {
            await page.click(selector, { timeout: 5000 });
            console.log(`Clicked "Yes" button with selector: ${selector}`);
            yesClicked = true;
            break;
          } catch (error) {
            console.log(`Failed to click with selector ${selector}:`, error.message);
          }
        }
        
        if (!yesClicked) {
          throw new Error('Could not find "Yes, ask me the questions" button');
        }
        console.log('Clicked "Yes, ask me the questions" button');
        await page.waitForTimeout(3000);
        await solveQuestions(page, initialUserTotalSrp);
      } else {
        // Look for direct "Start" button
        const startSelectors = [
          '#book-scroll div div div button',
          'button:has-text("Start")',
          'button:has-text("start")',
          '[data-test-id*="start"]',
          'button[class*="start"]'
        ];
        
        for (const selector of startSelectors) {
          try {
            await page.click(selector, { timeout: 3000 });
            console.log(`Clicked "Start" button with selector: ${selector}`);
            await page.waitForTimeout(3000);
            await solveQuestions(page, initialUserTotalSrp);
            break;
          } catch (error) {
            // Continue to next selector
          }
        }
      }
    }
    
  } catch (error) {
    console.error('Error continuing question flow:', error);
  }
}
