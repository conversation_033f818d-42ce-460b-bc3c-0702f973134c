const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');

async function testSimpleAuth() {
  console.log('🔐 Testing Simple Authentication...\n');
  
  try {
    const db = getDatabase();
    const auth = getAuthManager();
    
    // Find the test user
    const testUser = db.db.prepare(`
      SELECT * FROM users 
      WHERE username LIKE 'queuetest_%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `).get();
    
    if (!testUser) {
      console.log('❌ No test user found. Run test-queue-system.js first.');
      return;
    }
    
    console.log(`📋 Testing with user: ${testUser.username} (ID: ${testUser.id})`);
    
    // Create a session for the test user
    console.log('\n1. Creating session...');
    const sessionData = auth.createSession(testUser, '127.0.0.1', 'test-agent');
    const token = sessionData.token;
    console.log(`   ✅ Session created: ${token.substring(0, 20)}...`);
    
    // Test token validation
    console.log('\n2. Testing token validation...');
    try {
      const validatedSession = auth.validateSession(token);
      console.log(`   ✅ Token validated successfully`);
      console.log(`   User ID: ${validatedSession.userId}`);
      console.log(`   Username: ${validatedSession.username}`);
      console.log(`   Role: ${validatedSession.role}`);
    } catch (error) {
      console.log(`   ❌ Token validation failed: ${error.message}`);
      return;
    }
    
    // Test license features retrieval
    console.log('\n3. Testing license features...');
    const features = db.getUserLicenseFeatures(testUser.id);
    console.log(`   Max accounts per batch: ${features.max_accounts_per_batch}`);
    console.log(`   Priority level: ${features.priority_level}`);
    console.log(`   Scheduling access: ${features.scheduling_access}`);
    console.log(`   Max batches per day: ${features.max_batches_per_day}`);
    
    if (features.scheduling_access) {
      console.log('   ✅ User has scheduling access');
    } else {
      console.log('   ❌ User does not have scheduling access');
    }
    
    console.log('\n🎉 Simple auth test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testSimpleAuth();
}

module.exports = { testSimpleAuth };
