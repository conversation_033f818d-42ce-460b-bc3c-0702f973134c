const { getDatabase } = require('../lib/database');

function verifyAdminAccounts() {
  try {
    const dbManager = getDatabase();
    const db = dbManager.db;

    console.log('Listing all admin accounts:');
    
    const admins = db.prepare(`
      SELECT id, username, created_at 
      FROM users 
      WHERE role = 'admin'
      ORDER BY created_at
    `).all();

    admins.forEach(admin => {
      console.log(`- ${admin.username} (ID: ${admin.id}, Created: ${admin.created_at})`);
    });

    console.log(`\nTotal admin accounts: ${admins.length}`);

  } catch (error) {
    console.error('Failed to verify admin accounts:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  verifyAdminAccounts();
}
