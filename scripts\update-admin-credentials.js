const { getDatabase } = require('../lib/database');
const bcrypt = require('bcryptjs');
require('dotenv').config();

function updateAdminCredentials() {
  console.log('Updating admin credentials...');

  // Validate environment variables
  if (!process.env.DEFAULT_ADMIN_USERNAME || !process.env.DEFAULT_ADMIN_PASSWORD) {
    console.error('Error: Missing required environment variables');
    console.error('Please ensure .env contains DEFAULT_ADMIN_USERNAME and DEFAULT_ADMIN_PASSWORD');
    process.exit(1);
  }

  try {
    const dbManager = getDatabase();
    const db = dbManager.db;

    // Get current admin user
    const admin = db.prepare('SELECT * FROM users WHERE role = ? LIMIT 1').get('admin');
    
    if (!admin) {
      console.log('No admin user found - a new one will be created when the application starts');
      return;
    }

    // Hash new password
    const hashedPassword = bcrypt.hashSync(process.env.DEFAULT_ADMIN_PASSWORD, 12);

    // Update credentials
    const stmt = db.prepare(`
      UPDATE users 
      SET username = ?, password_hash = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    
    stmt.run(
      process.env.DEFAULT_ADMIN_USERNAME, 
      hashedPassword,
      admin.id
    );

    console.log('Successfully updated admin credentials');
    console.log(`Username set to: ${process.env.DEFAULT_ADMIN_USERNAME}`);
    console.log('Password has been updated');

  } catch (error) {
    console.error('Failed to update admin credentials:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  updateAdminCredentials();
}

module.exports = { updateAdminCredentials };
