const Database = require('better-sqlite3');
const path = require('path');

function migrateDatabase() {
  const dbPath = path.join(process.cwd(), 'data', 'app.db');
  const db = new Database(dbPath);
  
  console.log('Starting database migration...');
  
  try {
    // Check if encrypted_school column exists
    const tableInfo = db.prepare("PRAGMA table_info(encrypted_credentials)").all();
    const hasEncryptedSchool = tableInfo.some(col => col.name === 'encrypted_school');
    
    if (!hasEncryptedSchool) {
      console.log('Adding encrypted_school column to encrypted_credentials table...');
      db.exec('ALTER TABLE encrypted_credentials ADD COLUMN encrypted_school TEXT');
      console.log('✓ Added encrypted_school column');
    } else {
      console.log('✓ encrypted_school column already exists');
    }
    
    // Check if license_feature_settings table exists
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='license_feature_settings'").all();
    if (tables.length === 0) {
      console.log('Creating license_feature_settings table...');
      db.exec(`
        CREATE TABLE license_feature_settings (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          license_key_id INTEGER NOT NULL,
          max_accounts_per_batch INTEGER DEFAULT 0,
          priority_level INTEGER DEFAULT 0 CHECK(priority_level >= 0 AND priority_level <= 10),
          scheduling_access BOOLEAN DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (license_key_id) REFERENCES license_keys (id),
          UNIQUE(license_key_id)
        )
      `);
      console.log('✓ Created license_feature_settings table');
    } else {
      console.log('✓ license_feature_settings table already exists');
    }
    
    // Check if queue_batches table exists
    const queueBatchesExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='queue_batches'").all();
    if (queueBatchesExists.length === 0) {
      console.log('Creating queue_batches table...');
      db.exec(`
        CREATE TABLE queue_batches (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          batch_name TEXT,
          total_accounts INTEGER NOT NULL,
          processed_accounts INTEGER DEFAULT 0,
          failed_accounts INTEGER DEFAULT 0,
          status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
          priority_level INTEGER DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          started_at DATETIME,
          completed_at DATETIME,
          scheduled_time DATETIME,
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `);
      console.log('✓ Created queue_batches table');
    } else {
      console.log('✓ queue_batches table already exists');
    }
    
    // Check if queue_jobs table exists
    const queueJobsExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='queue_jobs'").all();
    if (queueJobsExists.length === 0) {
      console.log('Creating queue_jobs table...');
      db.exec(`
        CREATE TABLE queue_jobs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          batch_id INTEGER,
          user_id INTEGER NOT NULL,
          job_type TEXT NOT NULL DEFAULT 'sparx_reader',
          job_data TEXT NOT NULL,
          status TEXT DEFAULT 'queued' CHECK(status IN ('queued', 'processing', 'completed', 'failed', 'cancelled')),
          priority_level INTEGER DEFAULT 0,
          effective_priority INTEGER DEFAULT 0,
          scheduled_time DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          started_at DATETIME,
          completed_at DATETIME,
          error_message TEXT,
          retry_count INTEGER DEFAULT 0,
          max_retries INTEGER DEFAULT 3,
          FOREIGN KEY (batch_id) REFERENCES queue_batches (id),
          FOREIGN KEY (user_id) REFERENCES users (id)
        )
      `);
      console.log('✓ Created queue_jobs table');
    } else {
      console.log('✓ queue_jobs table already exists');
    }
    
    // Check if queue_schedules table exists
    const queueSchedulesExists = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='queue_schedules'").all();
    if (queueSchedulesExists.length === 0) {
      console.log('Creating queue_schedules table...');
      db.exec(`
        CREATE TABLE queue_schedules (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          scheduled_time DATETIME NOT NULL,
          duration_minutes INTEGER DEFAULT 30,
          job_id INTEGER,
          batch_id INTEGER,
          status TEXT DEFAULT 'scheduled' CHECK(status IN ('scheduled', 'active', 'completed', 'cancelled')),
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id),
          FOREIGN KEY (job_id) REFERENCES queue_jobs (id),
          FOREIGN KEY (batch_id) REFERENCES queue_batches (id)
        )
      `);
      console.log('✓ Created queue_schedules table');
    } else {
      console.log('✓ queue_schedules table already exists');
    }
    
    // Create indexes if they don't exist
    console.log('Creating indexes...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_license_features ON license_feature_settings(license_key_id)',
      'CREATE INDEX IF NOT EXISTS idx_queue_batches_user ON queue_batches(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_queue_batches_status ON queue_batches(status)',
      'CREATE INDEX IF NOT EXISTS idx_queue_batches_scheduled ON queue_batches(scheduled_time)',
      'CREATE INDEX IF NOT EXISTS idx_queue_jobs_batch ON queue_jobs(batch_id)',
      'CREATE INDEX IF NOT EXISTS idx_queue_jobs_user ON queue_jobs(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_queue_jobs_status ON queue_jobs(status)',
      'CREATE INDEX IF NOT EXISTS idx_queue_jobs_priority ON queue_jobs(effective_priority)',
      'CREATE INDEX IF NOT EXISTS idx_queue_jobs_scheduled ON queue_jobs(scheduled_time)',
      'CREATE INDEX IF NOT EXISTS idx_queue_schedules_user ON queue_schedules(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_queue_schedules_time ON queue_schedules(scheduled_time)'
    ];
    
    indexes.forEach(indexSql => {
      db.exec(indexSql);
    });
    console.log('✓ Created indexes');
    
    console.log('Database migration completed successfully!');
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    db.close();
  }
}

if (require.main === module) {
  migrateDatabase();
}

module.exports = { migrateDatabase };