import { NextResponse } from 'next/server';

// Global state for real-time updates
global.realtimeData = global.realtimeData || {
  isRunning: false,
  currentQuestion: '',
  currentAnswer: '',
  questionNumber: 0,
  srpEarned: 0,
  questionHistory: [],
  progress: 0,
  status: 'idle'
};

export async function GET(request) {
  try {
    return NextResponse.json({
      success: true,
      data: global.realtimeData
    });
  } catch (error) {
    console.error('Error getting realtime data:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const updates = await request.json();
    
    // Update global state
    global.realtimeData = {
      ...global.realtimeData,
      ...updates,
      lastUpdated: new Date().toISOString()
    };
    
    console.log('Realtime data updated:', global.realtimeData);
    
    return NextResponse.json({
      success: true,
      data: global.realtimeData
    });
  } catch (error) {
    console.error('Error updating realtime data:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Helper function to update realtime data from other routes
export function updateRealtimeData(updates) {
  global.realtimeData = {
    ...global.realtimeData,
    ...updates,
    lastUpdated: new Date().toISOString()
  };
}