const https = require('https');
const { URL } = require('url');

class WebhookManager {
  constructor() {
    this.discordWebhookUrl = 'https://discord.com/api/webhooks/1391133719351394314/VEY8LIMPErwXKx8ZGgsJvhLwbjfqHEtzhsbiZfwHb3aSUp9htUtWDy9mrW4N2LhuD6c9';
  }

  async sendDiscordNotification(title, description, color = 0x3498db, fields = []) {
    const embed = {
      title,
      description,
      color,
      timestamp: new Date().toISOString(),
      fields,
      footer: {
        text: 'SparxReader Queue System'
      }
    };

    const payload = {
      embeds: [embed]
    };

    try {
      await this.sendWebhook(this.discordWebhookUrl, payload);
      console.log('Discord notification sent:', title);
    } catch (error) {
      console.error('Failed to send Discord notification:', error.message);
    }
  }

  async sendLicenseViolation(username, violation, details) {
    await this.sendDiscordNotification(
      '🚫 License Violation Detected',
      `User **${username}** attempted an unauthorized action`,
      0xe74c3c, // Red color
      [
        { name: 'Violation Type', value: violation, inline: true },
        { name: 'Details', value: details, inline: false },
        { name: 'Timestamp', value: new Date().toLocaleString(), inline: true }
      ]
    );
  }

  async sendScheduleConflict(username, scheduledTime, conflictDetails) {
    await this.sendDiscordNotification(
      '⚠️ Schedule Conflict Detected',
      `Schedule conflict for user **${username}**`,
      0xf39c12, // Orange color
      [
        { name: 'Requested Time', value: new Date(scheduledTime).toLocaleString(), inline: true },
        { name: 'Conflict Details', value: conflictDetails, inline: false },
        { name: 'User', value: username, inline: true }
      ]
    );
  }

  async sendPriorityAdjustment(jobId, oldPriority, newPriority, reason, adminUser = null) {
    await this.sendDiscordNotification(
      '🔄 Priority Adjustment',
      `Job priority has been adjusted`,
      0x9b59b6, // Purple color
      [
        { name: 'Job ID', value: jobId.toString(), inline: true },
        { name: 'Old Priority', value: oldPriority.toString(), inline: true },
        { name: 'New Priority', value: newPriority.toString(), inline: true },
        { name: 'Reason', value: reason, inline: false },
        ...(adminUser ? [{ name: 'Admin User', value: adminUser, inline: true }] : [])
      ]
    );
  }

  async sendBatchCreated(username, batchName, accountCount, scheduledTime = null) {
    await this.sendDiscordNotification(
      '📦 New Batch Created',
      `User **${username}** created a new batch`,
      0x2ecc71, // Green color
      [
        { name: 'Batch Name', value: batchName, inline: true },
        { name: 'Account Count', value: accountCount.toString(), inline: true },
        { name: 'User', value: username, inline: true },
        ...(scheduledTime ? [{ name: 'Scheduled Time', value: new Date(scheduledTime).toLocaleString(), inline: false }] : [])
      ]
    );
  }

  async sendBatchCompleted(username, batchName, processedCount, failedCount, duration) {
    await this.sendDiscordNotification(
      '✅ Batch Completed',
      `Batch processing completed for **${username}**`,
      0x27ae60, // Dark green color
      [
        { name: 'Batch Name', value: batchName, inline: true },
        { name: 'Processed', value: processedCount.toString(), inline: true },
        { name: 'Failed', value: failedCount.toString(), inline: true },
        { name: 'Duration', value: `${Math.round(duration / 60)} minutes`, inline: true },
        { name: 'User', value: username, inline: true }
      ]
    );
  }

  async sendQueueAlert(alertType, message, details = {}) {
    const colors = {
      'high_load': 0xe67e22, // Orange
      'system_error': 0xe74c3c, // Red
      'maintenance': 0x3498db, // Blue
      'info': 0x95a5a6 // Gray
    };

    const icons = {
      'high_load': '🔥',
      'system_error': '💥',
      'maintenance': '🔧',
      'info': 'ℹ️'
    };

    const fields = Object.entries(details).map(([key, value]) => ({
      name: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: value.toString(),
      inline: true
    }));

    await this.sendDiscordNotification(
      `${icons[alertType] || 'ℹ️'} Queue System Alert`,
      message,
      colors[alertType] || 0x95a5a6,
      fields
    );
  }

  sendWebhook(webhookUrl, payload) {
    return new Promise((resolve, reject) => {
      const url = new URL(webhookUrl);
      const data = JSON.stringify(payload);

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(data),
          'User-Agent': 'SparxReader-Queue-System/1.0'
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(responseData);
          } else {
            reject(new Error(`Webhook request failed with status ${res.statusCode}: ${responseData}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Webhook request timed out'));
      });

      req.setTimeout(10000); // 10 second timeout
      req.write(data);
      req.end();
    });
  }
}

// Export singleton instance
let webhookInstance = null;

function getWebhookManager() {
  if (!webhookInstance) {
    webhookInstance = new WebhookManager();
  }
  return webhookInstance;
}

module.exports = { getWebhookManager, WebhookManager };