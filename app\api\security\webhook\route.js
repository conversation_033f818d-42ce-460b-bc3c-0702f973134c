import { NextResponse } from 'next/server';
import { DatabaseManager } from '../../../../lib/database.js';
import { AuthManager } from '../../../../lib/auth.js';

// Convert to CommonJS for middleware compatibility
const SecurityMiddleware = require('../../../../lib/securityMiddleware');

// Enhanced middleware function with security checks
async function withSecurityMiddleware(request, handler) {
  try {
    // Create mock req/res objects for middleware compatibility
    const req = {
      headers: {
        authorization: request.headers.get('authorization'),
        'x-fingerprint': request.headers.get('x-fingerprint'),
        'user-agent': request.headers.get('user-agent')
      },
      body: await request.json(),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    };
    
    // Enhanced authentication (optional for webhook)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const authManager = new AuthManager();
      
      try {
        const user = authManager.validateSession(token);
        if (user) {
          req.user = user;
        }
      } catch (error) {
        // Continue without user for anonymous security events
        console.warn('Invalid token in security webhook:', error.message);
      }
    }

    const db = new DatabaseManager();
    return await handler(request, req, db);
  } catch (error) {
    console.error('Security webhook middleware error:', error);
    return NextResponse.json({ error: 'Security validation failed' }, { status: 500 });
  }
}

// POST /api/security/webhook - Receive security events
export async function POST(request) {
  return withSecurityMiddleware(request, async (request, req, db) => {
    try {
      console.log('🔒 Security webhook received events');
      
      const { events, batchSize, timestamp } = req.body;
      
      if (!events || !Array.isArray(events)) {
        return NextResponse.json({ 
          success: false,
          error: 'Events array is required' 
        }, { status: 400 });
      }

      const fingerprint = req.headers['x-fingerprint'] || 'unknown';
      const ipAddress = req.ip;
      const userAgent = req.headers['user-agent'] || 'unknown';
      const userId = req.user?.userId || null;
      
      let processedEvents = 0;
      let criticalEvents = 0;
      
      // Process each security event
      for (const event of events) {
        try {
          const {
            eventType,
            details,
            severity = 'medium',
            timestamp: eventTimestamp,
            fingerprint: eventFingerprint,
            url,
            referrer
          } = event;
          
          // Validate event data
          if (!eventType || !details) {
            console.warn('Invalid security event:', event);
            continue;
          }
          
          // Determine severity level
          const severityLevel = ['low', 'medium', 'high', 'critical'].includes(severity) ? severity : 'medium';
          
          // Count critical events
          if (severityLevel === 'critical') {
            criticalEvents++;
          }
          
          // Enhanced event details
          const enhancedDetails = {
            originalDetails: details,
            url: url || 'unknown',
            referrer: referrer || 'unknown',
            batchInfo: {
              batchSize,
              batchTimestamp: timestamp
            },
            fingerprints: {
              header: fingerprint,
              event: eventFingerprint || fingerprint
            }
          };
          
          // Log security event to database
          db.logSecurityEvent(
            userId,
            eventType,
            eventFingerprint || fingerprint,
            null, // oldFingerprint
            ipAddress,
            userAgent,
            JSON.stringify(enhancedDetails),
            severityLevel
          );
          
          processedEvents++;
          
          // Special handling for critical events
          if (severityLevel === 'critical') {
            console.error(`🚨 CRITICAL SECURITY EVENT: ${eventType} - ${details}`);
            
            // If user is authenticated, log additional activity
            if (userId) {
              db.logActivity(
                userId,
                'SECURITY_ALERT',
                `Critical security event: ${eventType}`,
                ipAddress
              );
              
              // For certain critical events, consider invalidating sessions
              if (eventType === 'script_injection' || eventType === 'debugger_detected') {
                console.warn(`⚠️ Considering session invalidation for user ${userId} due to ${eventType}`);
                // Uncomment to auto-invalidate sessions:
                // const authManager = new AuthManager();
                // authManager.invalidateAllUserSessions(userId);
              }
            }
          }
          
        } catch (eventError) {
          console.error('Error processing security event:', eventError);
        }
      }
      
      // Log batch processing summary
      if (userId) {
        db.logActivity(
          userId,
          'SECURITY_BATCH',
          `Processed ${processedEvents} security events (${criticalEvents} critical)`,
          ipAddress
        );
      }
      
      console.log(`✅ Processed ${processedEvents}/${events.length} security events (${criticalEvents} critical)`);
      
      return NextResponse.json({
        success: true,
        message: 'Security events processed successfully',
        processed: processedEvents,
        total: events.length,
        critical: criticalEvents,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('❌ Error processing security webhook:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Failed to process security events: ' + error.message 
      }, { status: 500 });
    }
  });
}

// GET /api/security/webhook - Get security event statistics (admin only)
export async function GET(request) {
  return withSecurityMiddleware(request, async (request, req, db) => {
    try {
      // Require authentication for GET requests
      if (!req.user) {
        return NextResponse.json({ 
          success: false,
          error: 'Authentication required' 
        }, { status: 401 });
      }
      
      // Admin only for security statistics
      if (req.user.role !== 'admin') {
        return NextResponse.json({ 
          success: false,
          error: 'Admin access required' 
        }, { status: 403 });
      }
      
      // Get recent security events
      const recentEvents = db.getRecentSecurityEvents(req.user.userId, 24);
      
      // Get event type statistics
      const eventStats = {};
      const severityStats = { low: 0, medium: 0, high: 0, critical: 0 };
      
      recentEvents.forEach(event => {
        eventStats[event.event_type] = (eventStats[event.event_type] || 0) + 1;
        severityStats[event.severity] = (severityStats[event.severity] || 0) + 1;
      });
      
      return NextResponse.json({
        success: true,
        statistics: {
          totalEvents: recentEvents.length,
          eventTypes: eventStats,
          severityBreakdown: severityStats,
          timeRange: '24 hours'
        },
        recentEvents: recentEvents.slice(0, 10) // Last 10 events
      });

    } catch (error) {
      console.error('❌ Error fetching security statistics:', error);
      return NextResponse.json({ 
        success: false,
        error: 'Failed to fetch security statistics' 
      }, { status: 500 });
    }
  });
}
