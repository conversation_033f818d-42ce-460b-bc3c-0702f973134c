// Global browser context storage using globalThis for persistence
if (!globalThis.sparxBrowserContext) {
  globalThis.sparxBrowserContext = {
    browser: null,
    page: null
  };
}

export function setGlobalBrowser(browser, page) {
  globalThis.sparxBrowserContext.browser = browser;
  globalThis.sparxBrowserContext.page = page;
  console.log('Browser context set:', !!browser, !!page);
}

export function getGlobalBrowser() {
  return globalThis.sparxBrowserContext.browser;
}

export function getGlobalPage() {
  return globalThis.sparxBrowserContext.page;
}

export function clearGlobalBrowser() {
  globalThis.sparxBrowserContext.browser = null;
  globalThis.sparxBrowserContext.page = null;
  console.log('Browser context cleared');
}