const { getDatabase } = require('./lib/database');

async function testCredentialsSave() {
  console.log('Testing credentials save functionality...');
  
  try {
    const db = getDatabase();
    
    // Test data
    const testUserId = 1; // Assuming admin user exists
    const testLoginMethod = 'normal';
    const testSchool = 'Test School';
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';
    const testEncryptionKey = 'test-encryption-key';
    
    console.log('Attempting to save encrypted credentials...');
    
    // Try to save credentials
    const loginKey = db.saveEncryptedCredentials(
      testUserId,
      testLoginMethod,
      testSchool,
      testEmail,
      testPassword,
      testEncryptionKey
    );
    
    console.log('✅ Credentials saved successfully!');
    console.log('Generated login key:', loginKey);
    
    // Try to retrieve credentials
    console.log('Attempting to retrieve encrypted credentials...');
    const retrievedCredentials = db.getEncryptedCredentials(loginKey, testEncryptionKey);
    
    if (retrievedCredentials) {
      console.log('✅ Credentials retrieved successfully!');
      console.log('Retrieved data:', {
        loginMethod: retrievedCredentials.loginMethod,
        school: retrievedCredentials.school,
        email: retrievedCredentials.email,
        // Don't log password for security
        passwordLength: retrievedCredentials.password.length
      });
      
      // Verify data integrity
      if (retrievedCredentials.school === testSchool &&
          retrievedCredentials.email === testEmail &&
          retrievedCredentials.password === testPassword &&
          retrievedCredentials.loginMethod === testLoginMethod) {
        console.log('✅ Data integrity verified!');
      } else {
        console.log('❌ Data integrity check failed!');
      }
    } else {
      console.log('❌ Failed to retrieve credentials');
    }
    
    // Clean up test data
    console.log('Cleaning up test data...');
    const deleteStmt = db.db.prepare('DELETE FROM encrypted_credentials WHERE login_key = ?');
    deleteStmt.run(loginKey);
    console.log('✅ Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testCredentialsSave().then(() => {
  console.log('Test completed');
  process.exit(0);
}).catch((error) => {
  console.error('Test failed with error:', error);
  process.exit(1);
});