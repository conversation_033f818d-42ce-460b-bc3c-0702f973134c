import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getAuthManager } from '../../../../lib/auth';

export async function GET(request) {
  try {
    const auth = getAuthManager();
    
    // Check authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    const db = getDatabase();
    
    // Get user's saved credentials (without decrypting them)
    const credentials = db.getUserCredentials(session.userId);

    return NextResponse.json({
      success: true,
      credentials: credentials.map(cred => ({
        loginKey: cred.login_key,
        loginMethod: cred.login_method,
        createdAt: cred.created_at
      }))
    });

  } catch (error) {
    console.error('List credentials error:', error);
    return NextResponse.json(
      { success: false, error: error.message === 'Invalid session' ? 'Authentication required' : 'Internal server error' },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}