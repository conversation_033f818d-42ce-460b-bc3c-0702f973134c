'use client'

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    licenseKey: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [errorDetails, setErrorDetails] = useState([]);
  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setErrorDetails([]);

    try {
      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';
      const body = isLogin 
        ? { username: formData.username, password: formData.password }
        : formData;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      const data = await response.json();

      if (data.success) {
        if (isLogin) {
          localStorage.setItem('token', data.token);
          localStorage.setItem('user', JSON.stringify(data.user));
          router.push('/');
        } else {
          setError('Registration successful! Please login.');
          setIsLogin(true);
          setFormData({ username: '', password: '', licenseKey: '' });
        }
      } else {
        setError(data.error || 'An error occurred');
        if (data.details && Array.isArray(data.details)) {
          setErrorDetails(data.details);
        }
      }
    } catch (error) {
      setError('Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent"></div>
      
      <div className="relative z-10 w-full max-w-md mx-4">
        <div className="bg-gray-900 border border-gray-800 rounded-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-light text-white mb-2">
              {isLogin ? 'Welcome Back' : 'Create Account'}
            </h1>
            <p className="text-gray-400 text-sm">
              {isLogin ? 'Sign in to continue' : 'Register with your license key'}
            </p>
          </div>

          {error && (
            <div className="mb-6 p-3 bg-red-500/10 border border-red-500/30 rounded text-red-400 text-sm">
              <div className="font-medium">{error}</div>
              {errorDetails.length > 0 && (
                <ul className="mt-2 space-y-1 text-xs">
                  {errorDetails.map((detail, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-red-400 mr-1">•</span>
                      {detail}
                    </li>
                  ))}
                </ul>
              )}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-gray-300 text-sm mb-2">Username</label>
              <input
                type="text"
                value={formData.username}
                onChange={(e) => setFormData({...formData, username: e.target.value})}
                className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500"
                placeholder="Enter username"
                required
              />
              {!isLogin && (
                <div className="mt-1 text-xs text-gray-500">
                  3-20 characters, letters, numbers, and underscores only
                </div>
              )}
            </div>

            <div>
              <label className="block text-gray-300 text-sm mb-2">Password</label>
              <input
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({...formData, password: e.target.value})}
                className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500"
                placeholder="Enter password"
                required
              />
              {!isLogin && (
                <div className="mt-2 p-3 bg-gray-800/50 border border-gray-700 rounded text-xs text-gray-400">
                  <div className="font-medium text-gray-300 mb-1">Password Requirements:</div>
                  <ul className="space-y-1">
                    <li>• At least 8 characters long</li>
                    <li>• At least one uppercase letter (A-Z)</li>
                    <li>• At least one lowercase letter (a-z)</li>
                    <li>• At least one number (0-9)</li>
                    <li>• At least one special character (!@#$%^&*(),.?":{}|&lt;&gt;)</li>
                  </ul>
                </div>
              )}
            </div>

            {!isLogin && (
              <div>
                <label className="block text-gray-300 text-sm mb-2">License Key</label>
                <input
                  type="text"
                  value={formData.licenseKey}
                  onChange={(e) => setFormData({...formData, licenseKey: e.target.value})}
                  className="w-full px-4 py-3 bg-black border border-gray-700 rounded text-white placeholder-gray-500 focus:outline-none focus:border-blue-500"
                  placeholder="SRX-XXXXXXXX-XXXXXXXX"
                  required
                />
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className="w-full py-3 px-6 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white font-medium rounded transition-all duration-200 disabled:cursor-not-allowed"
            >
              {loading ? (
                <span className="flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  {isLogin ? 'Signing in...' : 'Creating account...'}
                </span>
              ) : (
                isLogin ? 'Sign In' : 'Create Account'
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <button
              onClick={() => {
                setIsLogin(!isLogin);
                setError('');
                setErrorDetails([]);
                setFormData({ username: '', password: '', licenseKey: '' });
              }}
              className="text-blue-400 hover:text-blue-300 text-sm transition-colors"
            >
              {isLogin ? "Don't have an account? Register" : "Already have an account? Sign in"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}