class TamperDetection {
  constructor(webhookUrl = '/api/security/webhook') {
    this.webhookUrl = webhookUrl;
    this.isActive = false;
    this.detectionEvents = [];
    this.originalConsole = {};
    this.devToolsOpen = false;
    this.lastHeartbeat = Date.now();
    this.fingerprintManager = null;
    
    // Bind methods
    this.detectDevTools = this.detectDevTools.bind(this);
    this.detectConsoleUsage = this.detectConsoleUsage.bind(this);
    this.detectDOMManipulation = this.detectDOMManipulation.bind(this);
    this.sendSecurityEvent = this.sendSecurityEvent.bind(this);
  }

  // Initialize tamper detection
  async initialize(fingerprintManager) {
    if (this.isActive) return;
    
    this.fingerprintManager = fingerprintManager;
    this.isActive = true;
    
    console.log('🛡️ Tamper detection initialized');
    
    // Start all detection methods
    this.detectDevTools();
    this.detectConsoleUsage();
    this.detectDOMManipulation();
    this.detectDebuggerUsage();
    this.detectScriptInjection();
    this.startHeartbeat();
    
    // Detect page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.sendSecurityEvent('page_hidden', 'User switched away from page', 'low');
      }
    });
    
    // Detect right-click context menu
    document.addEventListener('contextmenu', (e) => {
      this.sendSecurityEvent('context_menu', 'Right-click context menu accessed', 'low');
    });
    
    // Detect key combinations (F12, Ctrl+Shift+I, etc.)
    document.addEventListener('keydown', (e) => {
      const suspiciousKeys = [
        { key: 'F12' },
        { key: 'I', ctrlKey: true, shiftKey: true },
        { key: 'J', ctrlKey: true, shiftKey: true },
        { key: 'C', ctrlKey: true, shiftKey: true },
        { key: 'U', ctrlKey: true }
      ];
      
      for (const combo of suspiciousKeys) {
        if (e.key === combo.key && 
            (!combo.ctrlKey || e.ctrlKey) && 
            (!combo.shiftKey || e.shiftKey)) {
          e.preventDefault();
          this.sendSecurityEvent('suspicious_keypress', 
            `Suspicious key combination: ${e.key}${e.ctrlKey ? '+Ctrl' : ''}${e.shiftKey ? '+Shift' : ''}`, 
            'medium');
        }
      }
    });
  }

  // Detect developer tools
  detectDevTools() {
    const threshold = 160;
    
    setInterval(() => {
      const widthThreshold = window.outerWidth - window.innerWidth > threshold;
      const heightThreshold = window.outerHeight - window.innerHeight > threshold;
      const devToolsOpen = widthThreshold || heightThreshold;
      
      if (devToolsOpen && !this.devToolsOpen) {
        this.devToolsOpen = true;
        this.sendSecurityEvent('devtools_opened', 'Developer tools detected as opened', 'high');
      } else if (!devToolsOpen && this.devToolsOpen) {
        this.devToolsOpen = false;
        this.sendSecurityEvent('devtools_closed', 'Developer tools detected as closed', 'medium');
      }
    }, 1000);
    
    // Alternative detection method using console
    let devtools = {
      open: false,
      orientation: null
    };
    
    const element = new Image();
    Object.defineProperty(element, 'id', {
      get: function() {
        devtools.open = true;
        devtools.orientation = (window.outerHeight - window.innerHeight > window.outerWidth - window.innerWidth) ? 'vertical' : 'horizontal';
        if (!this.devToolsOpen) {
          this.sendSecurityEvent('devtools_console_access', 'Developer tools accessed via console', 'critical');
        }
      }.bind(this)
    });
    
    setInterval(() => {
      devtools.open = false;
      console.dir(element);
      console.clear();
    }, 2000);
  }

  // Detect console usage
  detectConsoleUsage() {
    // Store original console methods
    this.originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug,
      dir: console.dir,
      table: console.table
    };
    
    // Override console methods
    const self = this;
    ['log', 'warn', 'error', 'info', 'debug', 'dir', 'table'].forEach(method => {
      console[method] = function(...args) {
        self.sendSecurityEvent('console_usage', 
          `Console.${method} called with: ${JSON.stringify(args).substring(0, 200)}`, 
          'medium');
        return self.originalConsole[method].apply(console, args);
      };
    });
  }

  // Detect DOM manipulation
  detectDOMManipulation() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check for suspicious script tags
              if (node.tagName === 'SCRIPT') {
                this.sendSecurityEvent('script_injection', 
                  `Script tag injected: ${node.src || 'inline script'}`, 
                  'critical');
              }
              
              // Check for suspicious iframes
              if (node.tagName === 'IFRAME') {
                this.sendSecurityEvent('iframe_injection', 
                  `Iframe injected: ${node.src}`, 
                  'high');
              }
            }
          });
        }
        
        if (mutation.type === 'attributes') {
          // Check for suspicious attribute changes
          if (mutation.attributeName === 'onclick' || 
              mutation.attributeName === 'onload' || 
              mutation.attributeName.startsWith('on')) {
            this.sendSecurityEvent('suspicious_attribute', 
              `Suspicious attribute ${mutation.attributeName} modified on ${mutation.target.tagName}`, 
              'medium');
          }
        }
      });
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeOldValue: true
    });
  }

  // Detect debugger usage
  detectDebuggerUsage() {
    setInterval(() => {
      const start = performance.now();
      debugger; // This will pause if debugger is open
      const end = performance.now();
      
      if (end - start > 100) {
        this.sendSecurityEvent('debugger_detected', 
          'Debugger breakpoint detected', 
          'critical');
      }
    }, 3000);
  }

  // Detect script injection
  detectScriptInjection() {
    // Monitor for eval usage
    const originalEval = window.eval;
    window.eval = function(code) {
      this.sendSecurityEvent('eval_usage', 
        `eval() called with: ${code.substring(0, 200)}`, 
        'high');
      return originalEval.call(window, code);
    }.bind(this);
    
    // Monitor for Function constructor
    const originalFunction = window.Function;
    window.Function = function(...args) {
      this.sendSecurityEvent('function_constructor', 
        `Function constructor called with: ${JSON.stringify(args).substring(0, 200)}`, 
        'high');
      return originalFunction.apply(window, args);
    }.bind(this);
  }

  // Start heartbeat to detect if script is disabled
  startHeartbeat() {
    setInterval(() => {
      this.lastHeartbeat = Date.now();
    }, 5000);
    
    // Check if heartbeat is working
    setTimeout(() => {
      setInterval(() => {
        if (Date.now() - this.lastHeartbeat > 10000) {
          this.sendSecurityEvent('heartbeat_failure', 
            'Security heartbeat failed - possible script tampering', 
            'critical');
        }
      }, 15000);
    }, 10000);
  }

  // Send security event to webhook
  async sendSecurityEvent(eventType, details, severity = 'medium') {
    try {
      const fingerprint = this.fingerprintManager ? 
        await this.fingerprintManager.getFingerprint() : 'unknown';
      
      const event = {
        eventType,
        details,
        severity,
        timestamp: new Date().toISOString(),
        fingerprint,
        userAgent: navigator.userAgent,
        url: window.location.href,
        referrer: document.referrer
      };
      
      // Store locally for batch sending
      this.detectionEvents.push(event);
      
      // Send immediately for critical events
      if (severity === 'critical') {
        await this.flushEvents();
      } else if (this.detectionEvents.length >= 10) {
        // Batch send when we have 10 events
        await this.flushEvents();
      }
      
      console.warn(`🚨 Security Event [${severity.toUpperCase()}]: ${eventType} - ${details}`);
    } catch (error) {
      console.error('Failed to send security event:', error);
    }
  }

  // Flush events to webhook
  async flushEvents() {
    if (this.detectionEvents.length === 0) return;
    
    const events = [...this.detectionEvents];
    this.detectionEvents = [];
    
    try {
      const token = localStorage.getItem('auth_token');
      const fingerprint = this.fingerprintManager ? 
        await this.fingerprintManager.getFingerprint() : 'unknown';
      
      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-Fingerprint': fingerprint
        },
        body: JSON.stringify({
          events,
          batchSize: events.length,
          timestamp: new Date().toISOString()
        })
      });
      
      if (!response.ok) {
        console.error('Failed to send security events:', response.statusText);
        // Re-add events to queue for retry
        this.detectionEvents.unshift(...events);
      }
    } catch (error) {
      console.error('Error sending security events:', error);
      // Re-add events to queue for retry
      this.detectionEvents.unshift(...events);
    }
  }

  // Disable tamper detection
  disable() {
    this.isActive = false;
    
    // Restore original console methods
    Object.keys(this.originalConsole).forEach(method => {
      console[method] = this.originalConsole[method];
    });
    
    console.log('🛡️ Tamper detection disabled');
  }

  // Get detection statistics
  getStats() {
    return {
      isActive: this.isActive,
      eventsQueued: this.detectionEvents.length,
      devToolsOpen: this.devToolsOpen,
      lastHeartbeat: this.lastHeartbeat
    };
  }
}

// Global instance
let tamperDetection = null;

export function getTamperDetection() {
  if (!tamperDetection) {
    tamperDetection = new TamperDetection();
  }
  return tamperDetection;
}

export default TamperDetection;
