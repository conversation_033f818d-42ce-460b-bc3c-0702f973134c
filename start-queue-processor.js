const { getQueueProcessor } = require('./lib/queueProcessor');

console.log('🚀 Starting Queue Processor...');

// Start the queue processor
const processor = getQueueProcessor();
processor.start();

console.log('✅ Queue Processor started successfully!');
console.log('📋 The processor will check for jobs every 5 seconds');
console.log('🔄 Jobs will be processed in order based on priority and creation time');

// Keep the process running
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Queue Processor...');
  processor.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Queue Processor...');
  processor.stop();
  process.exit(0);
});

// Keep the process alive
setInterval(() => {
  // This keeps the Node.js process running
}, 1000);
