import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getAuthManager } from '../../../../lib/auth';

export async function POST(request) {
  try {
    const { loginKey } = await request.json();

    // Validate input
    if (!loginKey) {
      return NextResponse.json(
        { success: false, error: 'Login key is required' },
        { status: 400 }
      );
    }

    const auth = getAuthManager();
    
    // Check authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    const db = getDatabase();
    
    // Generate encryption key based on user session
    const encryptionKey = `${session.userId}-${process.env.JWT_SECRET || 'default-key'}`;
    
    // Get encrypted credentials
    const credentials = db.getEncryptedCredentials(loginKey, encryptionKey);

    if (!credentials) {
      return NextResponse.json(
        { success: false, error: 'Invalid login key or credentials not found' },
        { status: 404 }
      );
    }

    // Verify the credentials belong to the authenticated user
    if (credentials.userId !== session.userId) {
      return NextResponse.json(
        { success: false, error: 'Access denied' },
        { status: 403 }
      );
    }

    // Log activity
    db.logActivity(session.userId, 'CREDENTIALS_ACCESSED', `Accessed ${credentials.loginMethod} login credentials`);

    return NextResponse.json({
      success: true,
      credentials: {
        loginMethod: credentials.loginMethod,
        school: credentials.school,
        email: credentials.email,
        password: credentials.password
      }
    });

  } catch (error) {
    console.error('Get credentials error:', error);
    return NextResponse.json(
      { success: false, error: error.message === 'Invalid session' ? 'Authentication required' : 'Internal server error' },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}