# Sparx Reader Auto - Advanced Authentication System

A comprehensive Next.js application with advanced user authentication, license key management, and admin dashboard for Sparx Reader automation.

## 🚀 Features

### Authentication System
- **Secure User Registration** with license key validation
- **JWT-based Authentication** with session management
- **Password Strength Validation** with security requirements
- **Rate Limiting** for login/registration attempts
- **Session Management** with automatic cleanup
- **Multi-device Session Support**

### Admin Dashboard
- **License Key Management** - Generate, track, and deactivate keys
- **User Management** - View, activate/deactivate users
- **Advanced Analytics** - System statistics and activity logs
- **Real-time Activity Monitoring**
- **Comprehensive User Controls**

### Security Features
- **SQL Database** with Better-SQLite3 for performance
- **Password Hashing** with bcrypt (12 rounds)
- **JWT Token Security** with expiration
- **Rate Limiting** protection
- **Session Validation** middleware
- **Activity Logging** for audit trails

### Database Schema
- **Users Table** - User accounts with roles and license tracking
- **License Keys Table** - Serial key management with expiration
- **User Sessions Table** - Active session tracking
- **Activity Logs Table** - Comprehensive audit trail
- **System Settings Table** - Configuration management

## 📦 Installation

### Quick Setup
```bash
# Clone and setup everything
npm run setup
```

### Manual Setup
```bash
# Install dependencies
npm install

# Initialize database
npm run init-db

# Start development server
npm run dev
```

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env.local` and configure:

```env
# JWT Secret - CHANGE THIS IN PRODUCTION!
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Database Configuration
DATABASE_PATH=./data/app.db

# Admin Configuration
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=admin123

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_DURATION_DAYS=7
```

### Default Credentials
- **Admin Username:** `admin`
- **Admin Password:** `admin123`
- **⚠️ Change these immediately after first login!**

## 🏗️ Architecture

### Database Structure
```sql
-- Users with role-based access
users (id, username, password_hash, role, license_key_id, created_at, last_login, is_active)

-- License key management
license_keys (id, key_code, duration_days, max_uses, current_uses, expires_at, is_active, features)

-- Session tracking
user_sessions (id, user_id, token_hash, expires_at, ip_address, user_agent, is_active)

-- Activity monitoring
activity_logs (id, user_id, action, details, ip_address, created_at)
```

### API Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration with license key
- `POST /api/auth/logout` - Logout current session
- `DELETE /api/auth/logout` - Logout all sessions
- `GET /api/auth/validate` - Validate current session

#### Admin Management
- `GET /api/admin/keys` - List license keys
- `POST /api/admin/keys` - Create new license key
- `DELETE /api/admin/keys` - Deactivate license key
- `GET /api/admin/users` - List users
- `PATCH /api/admin/users` - Manage user status
- `GET /api/admin/analytics` - System statistics
- `GET /api/admin/addons` - Available features

## 🎯 Usage

### For Users
1. **Registration:** Obtain a license key from admin
2. **Login:** Use username, password, and license key
3. **Access:** Use the Sparx Reader automation features

### For Administrators
1. **Login:** Use admin credentials
2. **Generate Keys:** Create license keys with custom duration and features
3. **Manage Users:** View, activate/deactivate user accounts
4. **Monitor Activity:** Track system usage and user actions
5. **View Analytics:** System statistics and performance metrics

## 🔐 Security Features

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Rate Limiting
- **Login:** 5 attempts per 15 minutes per IP
- **Registration:** 3 attempts per hour per IP
- **Automatic cleanup** of expired rate limit entries

### Session Security
- **JWT tokens** with 7-day expiration
- **Database session tracking** for validation
- **IP address and user agent logging**
- **Automatic session cleanup**

## 📊 Admin Dashboard Features

### License Key Management
- Generate keys with custom duration (days)
- Set maximum usage limits
- Add feature flags/addons
- Track key usage and expiration
- Deactivate keys instantly

### User Management
- View all registered users
- See license key associations
- Track last login times
- Activate/deactivate accounts
- Force logout all user sessions

### Analytics Dashboard
- Total users and active users
- License key statistics
- Recent activity monitoring
- System performance metrics
- Activity timeline with filtering

## 🛠️ Development

### Database Management
```bash
# Initialize fresh database
npm run init-db

# The database file is located at: ./data/app.db
```

### Adding New Features
1. Update database schema in `lib/database.js`
2. Create API endpoints in `app/api/`
3. Add frontend components
4. Update admin dashboard if needed

### Security Considerations
- Always hash passwords with bcrypt
- Validate all user inputs
- Use parameterized queries
- Implement proper rate limiting
- Log security-relevant activities

## 📝 License Key Format
License keys follow the format: `SRX-XXXXXXXX-XXXXXXXX-XXXXXXXX-XXXXXXXX`

Example: `SRX-A1B2C3D4-E5F6G7H8-I9J0K1L2-M3N4O5P6`

## 🚨 Production Deployment

### Security Checklist
- [ ] Change default admin credentials
- [ ] Set secure JWT_SECRET
- [ ] Configure proper environment variables
- [ ] Enable HTTPS
- [ ] Set up proper database backups
- [ ] Configure rate limiting
- [ ] Review and test all security features

### Performance Optimization
- Database indexes are automatically created
- Session cleanup runs automatically
- Rate limiting uses in-memory storage (consider Redis for production)
- Activity logs can be cleaned up periodically

## 🤝 Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support
For issues and questions:
1. Check the activity logs in admin dashboard
2. Review the console for error messages
3. Ensure database permissions are correct
4. Verify environment configuration

---

**⚠️ Important:** This system includes comprehensive security features, but always review and test in your specific environment before production use.