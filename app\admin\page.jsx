'use client'

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function AdminDashboard() {
  const [user, setUser] = useState(null);
  const [activeTab, setActiveTab] = useState('keys');
  const [keys, setKeys] = useState([]);
  const [addons, setAddons] = useState([]);
  const [users, setUsers] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [queueConfig, setQueueConfig] = useState({
    maxConcurrentJobs: 3,
    browserTimeoutMinutes: 9
  });
  const [loading, setLoading] = useState(true);
  const [keyForm, setKeyForm] = useState({
    duration: '30',
    maxUses: '1',
    priorityLevel: '0',
    maxAccountsPerBatch: '10',
    schedulingAccess: false
  });
  const router = useRouter();

  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (!token || !userData) {
      router.push('/login');
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== 'admin') {
      router.push('/');
      return;
    }

    setUser(parsedUser);
    fetchData(token);
  }, []);

  const fetchData = async (token) => {
    try {
      const [keysRes, addonsRes, usersRes, queueRes] = await Promise.all([
        fetch('/api/admin/keys', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/admin/addons', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/admin/users', {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch('/api/admin/queue-config', {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      const keysData = await keysRes.json();
      const addonsData = await addonsRes.json();
      const usersData = await usersRes.json();
      const queueData = await queueRes.json();

      if (keysData.success) {
        setKeys(keysData.keys || []);
      }
      if (addonsData.success) {
        setAddons(addonsData.addons || []);
      }
      if (usersData.success) {
        setUsers(usersData.users || []);
      }
      if (queueData.success) {
        setQueueConfig(queueData.config || {
          maxConcurrentJobs: 3,
          browserTimeoutMinutes: 9
        });
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const createKey = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          duration: parseInt(keyForm.duration),
          maxUses: parseInt(keyForm.maxUses),
          priority_level: parseInt(keyForm.priorityLevel),
          max_accounts_per_batch: parseInt(keyForm.maxAccountsPerBatch),
          scheduling_access: keyForm.schedulingAccess
        })
      });

      const data = await response.json();
      if (data.success) {
        fetchData(token);
        setKeyForm({ duration: '30', maxUses: '1', selectedAddons: [] });
        alert(`License key created successfully!\nKey: ${data.key.keyCode}`);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error creating key:', error);
      alert('Failed to create license key');
    }
  };

  const toggleUserStatus = async (userId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userId,
          action: 'toggle_status'
        })
      });

      const data = await response.json();
      if (data.success) {
        fetchData(token);
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error toggling user status:', error);
      alert('Failed to toggle user status');
    }
  };

  const logoutAllUserSessions = async (userId) => {
    if (!confirm('Are you sure you want to logout all sessions for this user?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/users', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          userId,
          action: 'logout_all'
        })
      });

      const data = await response.json();
      if (data.success) {
        alert('All user sessions have been logged out');
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error logging out user sessions:', error);
      alert('Failed to logout user sessions');
    }
  };

  const deactivateLicenseKey = async (keyId) => {
    if (!confirm('Are you sure you want to deactivate this license key?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/keys', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ keyId, action: 'deactivate' })
      });

      const data = await response.json();
      if (data.success) {
        fetchData(token);
        alert('License key deactivated successfully');
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error deactivating license key:', error);
      alert('Failed to deactivate license key');
    }
  };

  const updateQueueConfig = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/admin/queue-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(queueConfig)
      });

      const data = await response.json();
      if (data.success) {
        alert('Queue configuration updated successfully!');
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error updating queue config:', error);
      alert('Failed to update queue configuration');
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    router.push('/login');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 to-transparent"></div>
      
      <div className="relative z-10">
        {/* Header */}
        <header className="border-b border-gray-800 p-6">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-light text-white">Admin Dashboard</h1>
              <p className="text-gray-400 text-sm">Manage license keys and system settings</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-300">Welcome, {user?.username}</span>
              <button
                onClick={logout}
                className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto p-6">
          {/* Tabs */}
          <div className="mb-8">
            <div className="border-b border-gray-800">
              <nav className="flex space-x-8">
                {[
                  { id: 'keys', label: 'License Keys' },
                  { id: 'users', label: 'Users' },
                  { id: 'queue', label: 'Queue Config' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-400'
                        : 'border-transparent text-gray-400 hover:text-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* License Keys Tab */}
          {activeTab === 'keys' && (
            <div className="space-y-6">
              {/* Create Key Form */}
              <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h2 className="text-xl font-medium text-white mb-6">Create New License Key</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-gray-300 text-sm mb-2">Duration (days)</label>
                    <input
                      type="number"
                      value={keyForm.duration}
                      onChange={(e) => setKeyForm({...keyForm, duration: e.target.value})}
                      className="w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                      min="1"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-300 text-sm mb-2">Max Uses</label>
                    <input
                      type="number"
                      value={keyForm.maxUses}
                      onChange={(e) => setKeyForm({...keyForm, maxUses: e.target.value})}
                      className="w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                      min="1"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-300 text-sm mb-2">Priority Level</label>
                    <input
                      type="number"
                      value={keyForm.priorityLevel}
                      onChange={(e) => setKeyForm({...keyForm, priorityLevel: e.target.value})}
                      className="w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                      min="0"
                      max="10"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm mb-2">Max Accounts Per Batch</label>
                    <input
                      type="number"
                      value={keyForm.maxAccountsPerBatch}
                      onChange={(e) => setKeyForm({...keyForm, maxAccountsPerBatch: e.target.value})}
                      className="w-full px-4 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                      min="0"
                      max="1000"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 text-sm mb-2">Scheduling Access</label>
                    <input
                      type="checkbox"
                      checked={keyForm.schedulingAccess}
                      onChange={(e) => setKeyForm({...keyForm, schedulingAccess: e.target.checked})}
                      className="mr-2"
                    />
                  </div>
                  
                  
                </div>
                
                <button
                  onClick={createKey}
                  className="mt-6 px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
                >
                  Generate Key
                </button>
              </div>

              {/* Keys List */}
              <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h2 className="text-xl font-medium text-white mb-6">Existing Keys</h2>
                
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-3 px-4 text-gray-300">Key Code</th>
                        <th className="text-left py-3 px-4 text-gray-300">Created</th>
                        <th className="text-left py-3 px-4 text-gray-300">Expires</th>
                        <th className="text-left py-3 px-4 text-gray-300">Uses</th>
                        <th className="text-left py-3 px-4 text-gray-300">Status</th>
                        <th className="text-left py-3 px-4 text-gray-300">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {keys.map((key) => (
                        <tr key={key.id} className="border-b border-gray-800">
                          <td className="py-3 px-4 text-white font-mono text-sm">{key.key_code}</td>
                          <td className="py-3 px-4 text-gray-300 text-sm">
                            {new Date(key.created_at).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4 text-gray-300 text-sm">
                            {new Date(key.expires_at).toLocaleDateString()}
                          </td>
                          <td className="py-3 px-4 text-gray-300 text-sm">
                            {key.users_count}/{key.max_uses}
                          </td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded text-xs ${
                              new Date(key.expires_at) > new Date() && key.is_active
                                ? 'bg-green-500/20 text-green-400'
                                : 'bg-red-500/20 text-red-400'
                            }`}>
                              {new Date(key.expires_at) > new Date() && key.is_active ? 'Active' : 'Expired'}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            {key.is_active && (
                              <button
                                onClick={() => deactivateLicenseKey(key.id)}
                                className="px-2 py-1 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded text-xs transition-colors"
                              >
                                Deactivate
                              </button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* Users Tab */}
          {activeTab === 'users' && (
            <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-medium text-white mb-6">User Management</h2>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="text-left py-3 px-4 text-gray-300">Username</th>
                      <th className="text-left py-3 px-4 text-gray-300">Role</th>
                      <th className="text-left py-3 px-4 text-gray-300">License Key</th>
                      <th className="text-left py-3 px-4 text-gray-300">Created</th>
                      <th className="text-left py-3 px-4 text-gray-300">Last Login</th>
                      <th className="text-left py-3 px-4 text-gray-300">Status</th>
                      <th className="text-left py-3 px-4 text-gray-300">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user) => (
                      <tr key={user.id} className="border-b border-gray-800">
                        <td className="py-3 px-4 text-white font-medium">{user.username}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${
                            user.role === 'admin' 
                              ? 'bg-purple-500/20 text-purple-400'
                              : 'bg-blue-500/20 text-blue-400'
                          }`}>
                            {user.role}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-300 font-mono text-sm">
                          {user.key_code || 'N/A'}
                        </td>
                        <td className="py-3 px-4 text-gray-300 text-sm">
                          {new Date(user.created_at).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4 text-gray-300 text-sm">
                          {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded text-xs ${
                            user.is_active
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-red-500/20 text-red-400'
                          }`}>
                            {user.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => toggleUserStatus(user.id)}
                              className={`px-2 py-1 rounded text-xs transition-colors ${
                                user.is_active
                                  ? 'bg-red-500/20 text-red-400 hover:bg-red-500/30'
                                  : 'bg-green-500/20 text-green-400 hover:bg-green-500/30'
                              }`}
                            >
                              {user.is_active ? 'Deactivate' : 'Activate'}
                            </button>
                            {user.role !== 'admin' && (
                              <button
                                onClick={() => logoutAllUserSessions(user.id)}
                                className="px-2 py-1 bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 rounded text-xs transition-colors"
                              >
                                Logout All
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                
                {users.length === 0 && (
                  <div className="text-center py-8 text-gray-400">
                    No users found
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Queue Configuration Tab */}
          {activeTab === 'queue' && (
            <div className="space-y-6">
              {/* Queue Settings */}
              <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h2 className="text-xl font-medium text-white mb-6">Queue Configuration</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Concurrent Jobs Setting */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Max Concurrent Jobs
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={queueConfig.maxConcurrentJobs}
                      onChange={(e) => setQueueConfig(prev => ({
                        ...prev,
                        maxConcurrentJobs: parseInt(e.target.value)
                      }))}
                      className="w-full px-3 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                    />
                    <p className="text-xs text-gray-400 mt-1">
                      Number of jobs that can process simultaneously (1-10)
                    </p>
                  </div>

                  {/* Browser Timeout Setting */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Browser Timeout (minutes)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="30"
                      value={queueConfig.browserTimeoutMinutes}
                      onChange={(e) => setQueueConfig(prev => ({
                        ...prev,
                        browserTimeoutMinutes: parseInt(e.target.value)
                      }))}
                      className="w-full px-3 py-2 bg-black border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                    />
                    <p className="text-xs text-gray-400 mt-1">
                      Auto-close browsers after this time to prevent stuck jobs
                    </p>
                  </div>
                </div>

                <div className="mt-6">
                  <button
                    onClick={updateQueueConfig}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                  >
                    Update Configuration
                  </button>
                </div>
              </div>

              {/* Queue Status */}
              <div className="bg-gray-900 border border-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-medium text-white mb-4">Current Queue Status</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-400">
                      {queueConfig.maxConcurrentJobs}
                    </div>
                    <div className="text-sm text-gray-400">Max Concurrent</div>
                  </div>
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="text-2xl font-bold text-green-400">
                      {queueConfig.browserTimeoutMinutes}m
                    </div>
                    <div className="text-sm text-gray-400">Browser Timeout</div>
                  </div>
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="text-2xl font-bold text-yellow-400">
                      Active
                    </div>
                    <div className="text-sm text-gray-400">Queue Status</div>
                  </div>
                </div>
              </div>
            </div>
          )}


        </div>
      </div>
    </div>
  );
}