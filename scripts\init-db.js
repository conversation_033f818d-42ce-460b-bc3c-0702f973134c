const { getDatabase } = require('../lib/database');

function initializeDatabase() {
  console.log('🚀 Initializing Sparx Reader Auto Database...\n');

  try {
    // Initialize database
    const db = getDatabase();
    console.log('✅ Database initialized successfully');
    console.log('✅ Tables created');
    console.log('✅ Indexes created');
    console.log('✅ Default admin user created');

    showSummary();

  } catch (error) {
    console.error('❌ Database initialization failed:', error);
    process.exit(1);
  }
}

function showSummary() {
  console.log('\n🎉 Database initialization complete!');
  console.log('\n📋 Summary:');
    console.log('   • Database file: ./data/app.db');
    console.log(`   • Default admin: ${process.env.DEFAULT_ADMIN_USERNAME} / [hidden]`);
    console.log('   • All tables and indexes created');
  console.log('\n🚀 You can now start the application with: npm run dev');
  console.log('\n⚠️  Remember to:');
  console.log('   • Change the default admin password');
  console.log('   • Set a secure JWT_SECRET in production');
  console.log('   • Configure proper environment variables');
}

// Run initialization
initializeDatabase();
