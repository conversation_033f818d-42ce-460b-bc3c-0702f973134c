const { getDatabase } = require('../lib/database');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function createAdminAccounts() {
  const accounts = [
    {
      username: 'alshababshabbyshabbab',
      password: 'shawarmaextrahummaswithtahin'
    },
    {
      username: 'rowdog',
      password: 'rowdogisreallycool'
    }
  ];

  try {
    const dbManager = getDatabase();
    const db = dbManager.db;

    for (const account of accounts) {
      // Check if user already exists
      const user = db.prepare('SELECT * FROM users WHERE username = ?').get(account.username);
      
      if (user) {
        console.log(`User ${account.username} already exists`);
        continue;
      }

      // Hash password
      const hashedPassword = bcrypt.hashSync(account.password, 12);

      // Create admin user
      const stmt = db.prepare(`
        INSERT INTO users (username, password_hash, role, is_active)
        VALUES (?, ?, 'admin', 1)
      `);
      
      stmt.run(account.username, hashedPassword);

      console.log(`Created admin account: ${account.username}`);
    }

  } catch (error) {
    console.error('Failed to create admin accounts:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  createAdminAccounts();
}
