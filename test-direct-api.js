// Load environment variables
require('dotenv').config();

const { getDatabase } = require('./lib/database');
const { getAuthManager } = require('./lib/auth');

async function testDirectAPI() {
  console.log('🌐 Testing Direct API Call...\n');
  
  try {
    const db = getDatabase();
    const auth = getAuthManager();
    
    // Find the test user
    const testUser = db.db.prepare(`
      SELECT * FROM users 
      WHERE username LIKE 'queuetest_%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `).get();
    
    if (!testUser) {
      console.log('❌ No test user found. Run test-queue-system.js first.');
      return;
    }
    
    console.log(`📋 Testing with user: ${testUser.username} (ID: ${testUser.id})`);
    
    // Create a fresh session
    console.log('\n1. Creating fresh session...');
    const sessionData = auth.createSession(testUser, '127.0.0.1', 'direct-test');
    const token = sessionData.token;
    console.log(`   ✅ Session created: ${token.substring(0, 20)}...`);
    
    // Test the session immediately
    console.log('\n2. Testing session validation...');
    try {
      const validatedSession = auth.validateSession(token);
      console.log(`   ✅ Session valid - User: ${validatedSession.username}, ID: ${validatedSession.userId}`);
    } catch (error) {
      console.log(`   ❌ Session validation failed: ${error.message}`);
      return;
    }
    
    // Test license features
    console.log('\n3. Testing license features...');
    const features = db.getUserLicenseFeatures(testUser.id);
    console.log(`   Features:`, features);
    
    // Make the actual API call using fetch
    console.log('\n4. Making API call...');
    const response = await fetch('http://localhost:3000/api/queue/status?detailed=true', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const responseText = await response.text();
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${responseText}`);
    
    if (response.ok) {
      const data = JSON.parse(responseText);
      if (data.license_features) {
        console.log('   ✅ License features found in response!');
        console.log('   License features:', data.license_features);
      } else {
        console.log('   ❌ No license_features in response');
      }
    } else {
      console.log('   ❌ API call failed');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testDirectAPI();
}

module.exports = { testDirectAPI };
