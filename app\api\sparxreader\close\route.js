import { NextResponse } from 'next/server';
import { getGlobalBrowser, clearGlobalBrowser } from '../browser-context.js';

export async function POST(request) {
  try {
    console.log('Closing browser session...');
    
    const browser = getGlobalBrowser();
    
    // Close the browser if it exists
    if (browser) {
      try {
        await browser.close();
        console.log('Browser closed successfully');
      } catch (error) {
        console.log('Error closing browser:', error.message);
      }
    }
    
    // Clear global variables
    clearGlobalBrowser();
    
    return NextResponse.json({ 
      success: true, 
      message: 'Browser session closed successfully'
    });
  } catch (error) {
    console.error('Error closing browser:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}