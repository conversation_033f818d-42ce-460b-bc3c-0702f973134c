@echo off
echo Setting up environment...

:: Set path to include Node.js
set PATH=C:\Program Files\nodejs;%PATH%

:: Change to the current directory
cd /d "%~dp0"

echo Installing Playwright browsers...
"C:\Program Files\nodejs\node.exe" "C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js" install -g playwright
"C:\Program Files\nodejs\node.exe" "C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js" exec playwright install chromium

echo Starting Next.js development server...
"C:\Program Files\nodejs\node.exe" "C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js" run dev

pause 