import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getAuthManager } from '../../../../lib/auth';

export async function POST(request) {
  try {
    const { username, password } = await request.json();

    // Validate input
    if (!username || !password) {
      return NextResponse.json(
        { success: false, error: 'Username and password are required' },
        { status: 400 }
      );
    }

    const db = getDatabase();
    const auth = getAuthManager();

    // Get client IP for rate limiting
    const clientIP = auth.getClientIP(request);
    const userAgent = auth.getUserAgent(request);

    // Check rate limiting
    const rateLimit = auth.checkRateLimit(`login_${clientIP}`, 5, 15);
    if (!rateLimit.allowed) {
      const resetTime = new Date(rateLimit.resetTime);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many login attempts. Please try again later.',
          resetTime: resetTime.toISOString()
        },
        { status: 429 }
      );
    }

    try {
      // Authenticate user
      const user = db.authenticateUser(username, password);
      
      // Create session
      const sessionData = auth.createSession(user, clientIP, userAgent);

      return NextResponse.json({
        success: true,
        token: sessionData.token,
        user: sessionData.user,
        expiresAt: sessionData.expiresAt
      });

    } catch (authError) {
      // Log failed login attempt
      db.logActivity(null, 'LOGIN_FAILED', `Failed login attempt for username: ${username}`, clientIP);
      
      return NextResponse.json(
        { success: false, error: authError.message },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}