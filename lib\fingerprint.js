import FingerprintJS from '@fingerprintjs/fingerprintjs';

class FingerprintManager {
  constructor() {
    this.fp = null;
    this.fingerprint = null;
    this.initialized = false;
  }

  // Initialize FingerprintJS
  async initialize() {
    if (this.initialized) return this.fingerprint;
    
    try {
      // Load FingerprintJS
      this.fp = await FingerprintJS.load();
      
      // Get the visitor identifier
      const result = await this.fp.get();
      this.fingerprint = result.visitorId;
      this.initialized = true;
      
      console.log('🔒 Fingerprint initialized:', this.fingerprint);
      return this.fingerprint;
    } catch (error) {
      console.error('❌ Fingerprint initialization failed:', error);
      // Fallback fingerprint based on browser characteristics
      this.fingerprint = this.generateFallbackFingerprint();
      this.initialized = true;
      return this.fingerprint;
    }
  }

  // Get current fingerprint
  async getFingerprint() {
    if (!this.initialized) {
      await this.initialize();
    }
    return this.fingerprint;
  }

  // Generate fallback fingerprint if FingerprintJS fails
  generateFallbackFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Fingerprint test', 2, 2);
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL(),
      navigator.hardwareConcurrency || 'unknown',
      navigator.deviceMemory || 'unknown'
    ].join('|');
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return 'fallback_' + Math.abs(hash).toString(36);
  }

  // Enhanced fingerprint with additional security checks
  async getEnhancedFingerprint() {
    const baseFingerprint = await this.getFingerprint();
    
    // Additional browser characteristics
    const enhanced = {
      base: baseFingerprint,
      timestamp: Date.now(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      plugins: Array.from(navigator.plugins).map(p => p.name).sort(),
      webgl: this.getWebGLFingerprint(),
      fonts: await this.getFontFingerprint(),
      audio: await this.getAudioFingerprint()
    };
    
    return enhanced;
  }

  // WebGL fingerprinting
  getWebGLFingerprint() {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) return 'no-webgl';
      
      const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
      return {
        vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
        renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL),
        version: gl.getParameter(gl.VERSION),
        shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
      };
    } catch (error) {
      return 'webgl-error';
    }
  }

  // Font detection fingerprinting
  async getFontFingerprint() {
    const testFonts = [
      'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
      'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
      'Trebuchet MS', 'Arial Black', 'Impact', 'Lucida Sans Unicode',
      'Tahoma', 'Lucida Console', 'Monaco', 'Courier', 'monospace'
    ];
    
    const availableFonts = [];
    const testString = 'mmmmmmmmmmlli';
    const testSize = '72px';
    const h = document.getElementsByTagName('body')[0];
    
    // Create test elements
    const s = document.createElement('span');
    s.style.fontSize = testSize;
    s.style.position = 'absolute';
    s.style.left = '-9999px';
    s.innerHTML = testString;
    
    const defaultWidth = {};
    const defaultHeight = {};
    
    // Get default measurements
    for (const font of ['monospace', 'sans-serif', 'serif']) {
      s.style.fontFamily = font;
      h.appendChild(s);
      defaultWidth[font] = s.offsetWidth;
      defaultHeight[font] = s.offsetHeight;
      h.removeChild(s);
    }
    
    // Test each font
    for (const font of testFonts) {
      let detected = false;
      for (const baseFont of ['monospace', 'sans-serif', 'serif']) {
        s.style.fontFamily = `"${font}",${baseFont}`;
        h.appendChild(s);
        const matched = (s.offsetWidth !== defaultWidth[baseFont] || 
                        s.offsetHeight !== defaultHeight[baseFont]);
        h.removeChild(s);
        detected = detected || matched;
      }
      if (detected) {
        availableFonts.push(font);
      }
    }
    
    return availableFonts.sort();
  }

  // Audio context fingerprinting
  async getAudioFingerprint() {
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const analyser = audioContext.createAnalyser();
      const gainNode = audioContext.createGain();
      const scriptProcessor = audioContext.createScriptProcessor(4096, 1, 1);
      
      oscillator.type = 'triangle';
      oscillator.frequency.setValueAtTime(10000, audioContext.currentTime);
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      
      oscillator.connect(analyser);
      analyser.connect(scriptProcessor);
      scriptProcessor.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      oscillator.start(0);
      
      return new Promise((resolve) => {
        scriptProcessor.onaudioprocess = function(bins) {
          const array = new Float32Array(analyser.frequencyBinCount);
          analyser.getFloatFrequencyData(array);
          
          let fingerprint = 0;
          for (let i = 0; i < array.length; i++) {
            fingerprint += Math.abs(array[i]);
          }
          
          oscillator.stop();
          scriptProcessor.disconnect();
          audioContext.close();
          
          resolve(fingerprint.toString());
        };
      });
    } catch (error) {
      return 'audio-error';
    }
  }

  // Validate fingerprint consistency
  validateFingerprint(storedFingerprint, currentFingerprint) {
    if (!storedFingerprint || !currentFingerprint) return false;
    
    // Allow for minor variations in enhanced fingerprints
    if (typeof currentFingerprint === 'object' && typeof storedFingerprint === 'object') {
      return currentFingerprint.base === storedFingerprint.base;
    }
    
    return storedFingerprint === currentFingerprint;
  }

  // Store fingerprint in localStorage
  storeFingerprint(fingerprint) {
    try {
      localStorage.setItem('app_fingerprint', JSON.stringify({
        fingerprint: fingerprint,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.warn('Failed to store fingerprint:', error);
    }
  }

  // Retrieve stored fingerprint
  getStoredFingerprint() {
    try {
      const stored = localStorage.getItem('app_fingerprint');
      if (stored) {
        const data = JSON.parse(stored);
        // Check if fingerprint is less than 7 days old
        if (Date.now() - data.timestamp < 7 * 24 * 60 * 60 * 1000) {
          return data.fingerprint;
        }
      }
    } catch (error) {
      console.warn('Failed to retrieve stored fingerprint:', error);
    }
    return null;
  }

  // Check for fingerprint changes
  async checkFingerprintChange() {
    const stored = this.getStoredFingerprint();
    const current = await this.getFingerprint();
    
    if (stored && !this.validateFingerprint(stored, current)) {
      console.warn('🚨 Fingerprint change detected!');
      return {
        changed: true,
        old: stored,
        new: current
      };
    }
    
    // Store current fingerprint
    this.storeFingerprint(current);
    
    return {
      changed: false,
      fingerprint: current
    };
  }
}

// Global instance
let fingerprintManager = null;

export function getFingerprintManager() {
  if (!fingerprintManager) {
    fingerprintManager = new FingerprintManager();
  }
  return fingerprintManager;
}

export default FingerprintManager;
