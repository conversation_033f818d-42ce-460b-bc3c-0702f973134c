const BASE_URL = "https://api.sparxext.com"
let validUser = true
let extensionEnabled = false

function createFloatingWindow() {
    // Add settings button
    const settingsButton = document.createElement("button")
    settingsButton.id = "sparxreader-settings-button"
    settingsButton.innerHTML = '<i class="fas fa-cog"></i>'
    settingsButton.style.cssText = `
        position: fixed;
        bottom: 70px;
        left: 20px;
        background: #4e63ff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        z-index: 9999;
    `
    document.body.appendChild(settingsButton)

    // Create settings menu
    const settingsMenu = document.createElement("div")
    settingsMenu.id = "sparxreader-settings-menu"
    settingsMenu.style.cssText = `
        position: fixed;
        bottom: 120px;
        left: 20px;
        background: #151a2d;
        border-radius: 8px;
        padding: 1rem;
        width: 200px;
        color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        display: none;
        z-index: 9998;
    `

    // Add delay time slider
    const delaySlider = document.createElement("div")
    delaySlider.style.cssText = `
        margin-bottom: 1rem;
        padding: 0.5rem;
    `
    delaySlider.innerHTML = `
        <label style="display: block; margin-bottom: 0.5rem;">Copy Page Delay (seconds)</label>
        <input type="range" id="delay-slider" min="1.5" max="10000" step="0.1" value="1.5" style="width: 100%;">
        <input type="number" id="delay-input" min="1.5" max="10000" step="0.1" style="width: 100px; margin-top: 0.5rem; background: #1f2937; color: white; border: 1px solid #374151; border-radius: 4px; padding: 4px 8px;">
        <span style="margin-left: 4px;">s</span>
    `
    settingsMenu.appendChild(delaySlider)

    // Update delay value display and store in localStorage
    const slider = delaySlider.querySelector("#delay-slider")
    const delayInput = delaySlider.querySelector("#delay-input")
    
    // Load saved value from localStorage
    const savedDelay = (parseInt(localStorage.getItem("copyPageDelay")) || 1500) / 1000
    slider.value = savedDelay
    delayInput.value = savedDelay

    slider.addEventListener("input", () => {
        delayInput.value = slider.value
        localStorage.setItem("copyPageDelay", slider.value * 1000)
    })

    delayInput.addEventListener("input", () => {
        const value = parseFloat(delayInput.value)
        if (value >= 1.5 && value <= 10000) {
            slider.value = value
            localStorage.setItem("copyPageDelay", value * 1000)
        }
    })

    document.body.appendChild(settingsMenu)

    settingsButton.addEventListener("click", () => {
        settingsMenu.style.display = settingsMenu.style.display === "none" ? "block" : "none"
    })

    settingsButton.addEventListener("mouseover", () => {
        settingsButton.style.transform = "scale(1.1)"
    })

    settingsButton.addEventListener("mouseout", () => {
        settingsButton.style.transform = "scaFle(1)"
    })

    // Add help button
    const helpButton = document.createElement("button")
    helpButton.id = "sparxreader-help-button"
    helpButton.innerHTML = '<i class="fas fa-question-circle"></i>'
    helpButton.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: #4e63ff;
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
        z-index: 9999;
    `
    document.body.appendChild(helpButton)

    helpButton.addEventListener("click", () => {
        showIntroModal()
    })

    helpButton.addEventListener("mouseover", () => {
        helpButton.style.transform = "scale(1.1)"
    })

    helpButton.addEventListener("mouseout", () => {
        helpButton.style.transform = "scale(1)"
    })
    if (!document.querySelector("#font-awesome-css")) {
        const fontAwesome = document.createElement("link")
        fontAwesome.id = "font-awesome-css"
        fontAwesome.rel = "stylesheet"
        fontAwesome.href =
            "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
        document.head.appendChild(fontAwesome)
    }

    const floatingWindow = document.createElement("div")
    floatingWindow.id = "math-solver-window"
    floatingWindow.innerHTML = `
        <div class="window-header">
            <div class="header-title">
                <i class="fas fa-book bolt-icon"></i>
                <span class="window-title">SPARXEXT</span>
            </div>
            <button class="minimize-btn"><i class="fas fa-minus"></i></button>
            <button class="close-btn"><i class="fas fa-times"></i></button>
        </div>

        <div class="window-content">
            <div class="menu-options">
                <div class="menu-option reader active" data-mode="reader">
                    <svg width="250" height="60" viewBox="0 0 300 60" xmlns="http://www.w3.org/2000/svg">
                        <!-- Background (dark) -->
                        <rect width="100%" height="100%" fill="#151a2d" />

                        <!-- Spark/star icon -->
                        <g transform="translate(70, 30)">
                            <polygon points="0,-10 3,-3 10,0 3,3 0,10 -3,3 -10,0 -3,-3" fill="#f59e0b" />
                            <circle cx="0" cy="0" r="2" fill="#fbbf24" />
                        </g>

                        <!-- Text -->
                        <text x="100" y="38" font-family="Segoe UI, sans-serif" font-size="24" fill="#f3f4f6" font-weight="bold">
                            sparx
                        </text>
                        <text x="170" y="38" font-family="Segoe UI, sans-serif" font-size="24" fill="#d1d5db">
                            reader
                        </text>
                    </svg>
                </div>
            </div>

            <div class="menu-settings-container" style="display: flex; flex-direction: column; gap: 1rem; align-items: flex-start;">
                <div class="settings-bar" style="display: flex; flex-direction: column; gap: 1rem; width: 100%;">
                    <div class="action-buttons" style="display: flex; flex-direction: row; gap: 1rem; width: 100%; justify-content: center;">
                        <button class="screenshot-button" title="Take Screenshot"
                            style="padding: 0.5rem 1rem; font-size: 14px; display: flex; flex-direction: row; align-items: center; gap: 0.5rem; border-radius: 4px; white-space: nowrap; background-color: #4e63ff; border: 1px solid #3f50cc; min-width: fit-content; box-sizing: border-box; color: white;">
                            <i class="fas fa-camera"></i>
                            <span>Take Screenshot</span>
                        </button>
                        <button class="copy-page-button" title="Copy Page Content"
                            style="padding: 0.5rem 1rem; font-size: 14px; display: flex; flex-direction: row; align-items: center; gap: 0.5rem; border-radius: 4px; white-space: nowrap; background-color: #4e63ff; border: 1px solid #3f50cc; min-width: fit-content; box-sizing: border-box; color: white;">
                            <i class="fas fa-copy"></i>
                            <span>Copy Page</span>
                        </button>
                    </div>
                    <div class="modern-checkbox-container" style="display: flex; flex-direction: row; gap: 1rem; justify-content: center; width: 100%; align-items: center;">
                        <label class="modern-checkbox" style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input type="checkbox" id="answer-only-toggle">
                            <span class="checkbox-box" style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; border: 1px solid #ccc; border-radius: 3px;">
                                <i class="fas fa-check checkbox-bolt" style="font-size: 12px; display: none;"></i>
                            </span>
                            <span class="checkbox-label">Answer Only</span>
                        </label>
                        <label class="modern-checkbox" style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input type="checkbox" id="automatic-toggle">
                            <span class="checkbox-box" style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; border: 1px solid #ccc; border-radius: 3px;">
                                <i class="fas fa-check checkbox-bolt" style="font-size: 12px; display: none;"></i>
                            </span>
                            <span class="checkbox-label">Automatic</span>
                        </label>
                        <label class="modern-checkbox" style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                            <input type="checkbox" id="auto-answer-toggle">
                            <span class="checkbox-box" style="display: inline-flex; align-items: center; justify-content: center; width: 18px; height: 18px; border: 1px solid #ccc; border-radius: 3px;">
                                <i class="fas fa-check checkbox-bolt" style="font-size: 12px; display: none;"></i>
                            </span>
                            <span class="checkbox-label">Auto Answer</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="result-area">
                <div id="loading" style="display: none;">
                    <i class="fas fa-circle-notch fa-spin"></i>
                    Processing...
                </div>
                <div id="solution"></div>
            </div>
        </div>
    `

    document.body.appendChild(floatingWindow)
    makeDraggable(floatingWindow)
    setupEventListeners(floatingWindow)
    addAutoAnswerToggle()
}

function makeDraggable(element) {
    let pos1 = 0,
        pos2 = 0,
        pos3 = 0,
        pos4 = 0
    const header = element.querySelector(".window-header")
    header.onmousedown = dragMouseDown
    function dragMouseDown(e) {
        e.preventDefault()
        pos3 = e.clientX
        pos4 = e.clientY
        document.onmouseup = closeDragElement
        document.onmousemove = elementDrag
    }
    function elementDrag(e) {
        e.preventDefault()
        pos1 = pos3 - e.clientX
        pos2 = pos4 - e.clientY
        pos3 = e.clientX
        pos4 = e.clientY
        element.style.top = element.offsetTop - pos2 + "px"
        element.style.left = element.offsetLeft - pos1 + "px"
    }
    function closeDragElement() {
        document.onmouseup = null
        document.onmousemove = null
    }
}

function setupEventListeners(window) {
    const minimizeBtn = window.querySelector(".minimize-btn")
    const closeBtn = window.querySelector(".close-btn")
    const screenshotBtn = window.querySelector(".screenshot-button")
    const copyPageBtn = window.querySelector(".copy-page-button")
    const content = window.querySelector(".window-content")
    const menuOptions = window.querySelectorAll(".menu-option")
    const solution = window.querySelector("#solution")
    const automaticToggle = document.getElementById("automatic-toggle")
    let pageContent = ""
    let isAutomationRunning = false

    // Set up mutation observer to watch for retry button
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1 && node.matches('button.sr_a4ad8eed')) {
                        const retryDiv = node.querySelector('div')
                        if (retryDiv && retryDiv.textContent === 'Retry') {
                            node.click()
                        }
                    }
                })
            }
        })
    })

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true })

    automaticToggle.addEventListener('change', async function() {
        const answerOnlyToggle = document.getElementById('answer-only-toggle');
        const autoAnswerToggle = document.getElementById('auto-answer-toggle');
        
        if (this.checked) {
            answerOnlyToggle.checked = true;
            autoAnswerToggle.checked = true;
            setupAutoAnswer(true);
            isAutomationRunning = true;
            while (this.checked) {
                // Check SRP progress and stop if target reached
                const checkSrpProgress = () => {
                    const srpElement = document.querySelector('.sr_32ae1144');
                    if (srpElement) {
                        const currentSrp = parseInt(srpElement.textContent.replace(/[^\d,]/g, '').replace(',', ''));
                        const initialSrp = parseInt(localStorage.getItem('initialSrp') || '0');
                        const targetSrp = parseInt(localStorage.getItem('targetSrp') || '0');
                        
                        if (targetSrp > 0 && initialSrp > 0) {
                            const srpEarned = currentSrp - initialSrp;
                            console.log(`SRP Progress: Initial: ${initialSrp}, Current: ${currentSrp}, Earned: ${srpEarned}, Target: ${targetSrp}`);
                            
                            if (srpEarned >= targetSrp) {
                                console.log(`Target SRP reached! Earned ${srpEarned} SRP (target was ${targetSrp})`);
                                // Turn off automatic toggle to stop automation
                                this.checked = false;
                                alert(`Congratulations! You've earned ${srpEarned} SRP and reached your target of ${targetSrp} SRP!`);
                                return true; // Target reached
                            }
                        }
                    }
                    return false; // Target not reached
                };
                
                // Check SRP progress before continuing
                if (checkSrpProgress()) {
                    break; // Exit the automation loop
                }
                
                // Click Start Reading button
                const startReadingBtn = Array.from(document.querySelectorAll('div')).find(div => 
                    div.classList.contains('sr_6f4b8d9a') && div.textContent.includes('Start Reading')
                );
                if (startReadingBtn) {
                    startReadingBtn.click();
                    // Add delay after first Start Reading button click
                    await new Promise(r => setTimeout(r, 1000));
                }

                // Wait and click the second Start Reading button
                await new Promise(r => setTimeout(r, 500));
                const secondStartReadingBtn = Array.from(document.querySelectorAll('div')).find(div => 
                    div.textContent.trim() === 'Start Reading'
                );
                if (secondStartReadingBtn) {
                    secondStartReadingBtn.click();
                    // Add delay after second Start Reading button click
                    await new Promise(r => setTimeout(r, 1000));
                }

                // Wait and click Copy Page button with custom delay
                const copyPageDelay = parseInt(localStorage.getItem("copyPageDelay")) || 1500;
                await new Promise(r => setTimeout(r, copyPageDelay));
                copyPageBtn.click();

                // Series of Get Answer clicks with delays
                for (let i = 0; i < 4; i++) {
                    const delay = i === 0 ? 2000 : 8000;
                    await new Promise(r => setTimeout(r, delay));
                    screenshotBtn.click();
                }

                // Check SRP progress after completing questions
                if (checkSrpProgress()) {
                    break; // Exit the automation loop if target reached
                }

                // Wait before next iteration
                await new Promise(r => setTimeout(r, 2000));
            }
            isAutomationRunning = false;
        }
    })

    minimizeBtn.addEventListener("click", () => {
        content.style.display =
            content.style.display === "none" ? "block" : "none"
    })
    closeBtn.addEventListener("click", () => {
        window.remove()
    })
    screenshotBtn.innerHTML = '<i class="fas fa-camera"></i>'
    screenshotBtn.title = "Take Screenshot"
    screenshotBtn.addEventListener("click", () => {
        const currentMode = document.querySelector(".menu-option.active")
            .dataset.mode
        if (currentMode === "reader") {
            const questionData = getQuestionText()
            if (questionData && questionData.fullText) {
                sendToGeminiText(questionData.fullText)
            } else {
                const solution = document.getElementById("solution")
                solution.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>Could not find question text</p>
                    <ul>
                        <li>Make sure you are on a question page</li>
                        <li>The question should start with 'Q' and end with '?' or '.'</li>
                    </ul>
                </div>
            `
            }
        } else {
            takeScreenshot()
        }
    })
    copyPageBtn.addEventListener("click", async () => {
        try {
            pageContent = document.body.innerText
            copyPageBtn.classList.add('copy-success');
            setTimeout(() => copyPageBtn.classList.remove('copy-success'), 300);
            solution.innerHTML = `
                <div class="success-message">
                    <i class="fas fa-check-circle"></i>
                    <p>Page Content Captured</p>
                    <ul>
                        <li>Page content has been stored for context</li>
                        <li>Click the tick button to get an answer</li>
                    </ul>
                </div>
            `
            chrome.runtime.sendMessage({
                action: "storePageContent",
                content: pageContent,
            })
            
            const readButton = document.querySelector('button[data-test-id="read-button"]')
            if (readButton) {
                readButton.click()
                setTimeout(() => {
                    const askQuestionsButton = Array.from(document.querySelectorAll('div')).find(div => 
                        div.textContent.trim() === 'Yes, ask me the questions.'
                    )
                    if (askQuestionsButton) {
                        askQuestionsButton.click()
                    } else {
                        const startButton = Array.from(document.querySelectorAll('div')).find(div => div.textContent.trim() === 'Start')
                        if (startButton) {
                            startButton.click()
                        }
                    }
                }, 1000) 
            }
        } catch (err) {
            console.error("Error copying page content:", err)
            solution.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>Error capturing page content</p>
                    <ul>
                        <li>Please try again</li>
                        <li>Make sure you're on the correct page</li>
                    </ul>
                </div>
            `
        }
    })
    menuOptions.forEach((option) => {
        option.addEventListener("click", () => {
            menuOptions.forEach((opt) => opt.classList.remove("active"))
            option.classList.add("active")
            if (option.dataset.mode === "reader") {
                copyPageBtn.style.display = "flex"
                screenshotBtn.innerHTML =
                    '<i class="fas fa-rocket"></i> Get Answer'
                screenshotBtn.title = "Get Answer"
                solution.innerHTML = `
                    <div class="info-message">
                        <i class="fas fa-info-circle"></i>
                        <p>Reader Mode Active</p>
                        <ul>
                            <li>First, click the "Copy Page" to capture the page content</li>
                            <li>Then click the "Get Answer" button to get the answer</li>
                            <li>Get AI-powered answers based on the context</li>
                        </ul>
                    </div>
                `
            } else {
                copyPageBtn.style.display = "none"
                screenshotBtn.innerHTML = '<i class="fas fa-camera"></i>'
                screenshotBtn.title = "Take Screenshot"
                solution.innerHTML = ""
                pageContent = ""
            }
        })
    })
}

function getQuestionText() {
    try {
        // Find the question container - usually it's a div containing both question and options
        const questionContainer = Array.from(
            document.querySelectorAll("div")
        ).find((div) => {
            const text = div.textContent

            // Look for text that contains Q followed by a number (e.g., Q1, Q2) or blanks (_____) or "Not in story"
            return (
                text.includes("Not in story") ||
                /Q\d+\./.test(text) ||
                text.includes("_____") ||
                text.includes("______")
            )
        })

        if (questionContainer) {
            console.log(
                "Found question container:",
                questionContainer.textContent
            ) // Debug log
            // Get all text nodes and elements within the container
            const allElements = Array.from(
                questionContainer.querySelectorAll("*")
            )
            const allOptions = new Set()

            // First, find the question text - improved detection
            const questionElement = allElements.find((el) => {
                const text = el.textContent.trim()
                console.log("Checking element text:", text) // Debug log
                // Check for numbered questions (Q1, Q2, etc) or questions with blanks
                return (
                    /Q\d+\./.test(text) ||
                    text.includes("_____") ||
                    text.includes("______")
                )
            })

            let questionText = ""
            if (questionElement) {
                questionText = questionElement.textContent.trim()
                console.log("Found question text:", questionText) // Debug log
            } else {
                // Fallback: try to find the question in the container's direct text
                const containerText = questionContainer.textContent
                // Match either Q followed by number or text containing blanks
                const matches = containerText.match(
                    /Q\d+\.[^?]*[?.]|[^?.]*_+[^?.]*[?.]/
                )
                if (matches) {
                    questionText = matches[0].trim()
                    console.log(
                        "Found question text using fallback:",
                        questionText
                    ) // Debug log
                }
            }

            if (!questionText) {
                console.log("No question text found") // Debug log
                return null
            }

            // Then get all options
            allElements.forEach((el) => {
                const text = el.textContent.trim()
                if (
                    text &&
                    (text === "Not in story" ||
                        el.tagName === "BUTTON" ||
                        el.getAttribute("role") === "button" ||
                        el.classList.contains("bg-gray-100"))
                ) {
                    if (
                        !text.toLowerCase().includes("read") &&
                        !text.toLowerCase().includes("continue") &&
                        !text.toLowerCase().includes("next")
                    ) {
                        allOptions.add(text)
                    }
                }
            })

            // Convert Set to Array and ensure "Not in story" is first if present
            const optionsArray = Array.from(allOptions)
            const notInStoryIndex = optionsArray.indexOf("Not in story")
            if (notInStoryIndex > -1) {
                optionsArray.splice(notInStoryIndex, 1)
                optionsArray.unshift("Not in story")
            }

            // Format the text
            const formattedText = `Question: ${questionText}\n\nOptions:\n${optionsArray.join("\n")}`

            console.log("Final formatted text:", formattedText) // Debug log

            return {
                question: questionText,
                options: optionsArray,
                fullText: formattedText,
            }
        }
    } catch (error) {
        console.error("Error capturing question:", error)
    }
    return null
}

async function sendToGeminiText(text) {
    const boltIcon = document.querySelector(".window-header .bolt-icon")
    const solution = document.getElementById("solution")
    const answerOnlyMode =
        document.getElementById("answer-only-toggle")?.checked

    try {
        boltIcon.classList.add("loading")
        solution.innerHTML = `
            <div class="info-message">
                <i class="fas fa-circle-notch fa-spin"></i>
                <p>Processing your question...</p>
            </div>
        `

        // Send text data to Gemini
        chrome.runtime.sendMessage(
            {
                action: "solveWithGemini",
                mode: "reader",
                answerOnly: answerOnlyMode,
                textContent: text,
                pageContent: document.body.innerText,
            },
            (response) => {
                if (chrome.runtime.lastError) {
                    console.error("Runtime error:", chrome.runtime.lastError)
                    throw new Error(
                        "Failed to communicate with background script"
                    )
                }

                if (response && response.error) {
                    throw new Error(response.error)
                }
            }
        )
    } catch (err) {
        console.error("Error sending text:", err)
        boltIcon.classList.remove("loading")
        solution.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Could not process the question. Please try:</p>
                <ul>
                    <li>Making sure you're on a question page</li>
                    <li>Checking your internet connection</li>
                    <li>Stop spamming the answer button</li>
                </ul>
            </div>
        `
    }
}

async function takeScreenshot() {
    const floatingWindow = document.getElementById("math-solver-window")
    const solution = document.getElementById("solution")
    const currentMode = document.querySelector(".menu-option.active").dataset
        .mode

    try {
        floatingWindow.classList.add("fade-out")
        await new Promise((resolve) => setTimeout(resolve, 300))

        const stream = await navigator.mediaDevices.getDisplayMedia({
            preferCurrentTab: true,
            video: {
                width: { ideal: 1920 },
                height: { ideal: 1080 },
            },
        })

        const video = document.createElement("video")
        video.srcObject = stream
        await new Promise((resolve) => {
            video.onloadedmetadata = () => {
                video.play()
                resolve()
            }
        })

        const canvas = document.createElement("canvas")
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight
        const ctx = canvas.getContext("2d")
        ctx.drawImage(video, 0, 0)

        stream.getTracks().forEach((track) => track.stop())
        const imageData = canvas.toDataURL("image/jpeg", 0.9)

        floatingWindow.classList.remove("fade-out")
        floatingWindow.classList.add("fade-in")
        setTimeout(() => floatingWindow.classList.remove("fade-in"), 300)

        // Add green bolt to appropriate button based on mode
        if (currentMode === "maths" || currentMode === "science") {
            // Remove any existing answer bolts
            document
                .querySelectorAll(".answer-bolt")
                .forEach((bolt) => bolt.remove())

            // Find the appropriate button based on mode
            const buttonText = currentMode === "maths" ? "answer" : "submit"
            const targetButton = Array.from(
                document.querySelectorAll("button")
            ).find(
                (button) =>
                    button.textContent.trim().toLowerCase() === buttonText
            )

            if (targetButton && !targetButton.querySelector(".answer-bolt")) {
                const bolt = document.createElement("i")
                bolt.className = "fas fa-check answer-bolt"
                bolt.style.cssText = `
                    color: #4ade80;
                    margin-right: 8px;
                    animation: answerBoltGlow 2s ease-in-out infinite;
                    font-size: 16px;
                    pointer-events: none; /* Prevent direct clicking of the bolt */
                `
                targetButton.insertBefore(bolt, targetButton.firstChild)

                // Add click handler with delay to the button
                let canClick = true
                targetButton.addEventListener("click", (e) => {
                    if (!canClick) {
                        e.preventDefault()
                        e.stopPropagation()
                        return
                    }

                    canClick = false
                    setTimeout(() => {
                        canClick = true
                    }, 0) // 1 second delay before next click
                })
            }
        }

        sendToGemini(imageData)
    } catch (err) {
        console.error("Error taking screenshot:", err)
        floatingWindow.classList.remove("fade-out")
        floatingWindow.classList.add("fade-in")
        setTimeout(() => floatingWindow.classList.remove("fade-in"), 300)
        solution.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Error taking screenshot</p>
                <ul>
                    <li>Please make sure to select the area containing the math problem</li>
                    <li>Try again by clicking the camera button</li>
                </ul>
            </div>
        `
    }
}

let autoAnswerObserver = null
let isProcessing = false // Add debounce flag

function setupAutoAnswer(enabled) {
    if (autoAnswerObserver) {
        autoAnswerObserver.disconnect()
        autoAnswerObserver = null
    }
    if (!enabled) return

    autoAnswerObserver = new MutationObserver((mutations) => {
        const isEnabled = document.getElementById("auto-answer-toggle")?.checked
        if (!isEnabled || isProcessing) return

        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach((node) => {
                    // Check for both page-correct-answer and answer-bolt classes
                    if (
                        (node.classList &&
                            (node.classList.contains("page-correct-answer") ||
                                node.classList.contains("answer-bolt"))) ||
                        (node.classList &&
                            node.classList.contains("solution-text"))
                    ) {
                        isProcessing = true // Set processing flag

                        // Wait 1 second before taking any action
                        setTimeout(() => {
                            // First try to find and click any answer button with the green bolt
                            const answerButtonWithBolt = Array.from(
                                document.querySelectorAll("button")
                            ).find((button) =>
                                button.querySelector(".answer-bolt")
                            )

                            if (answerButtonWithBolt) {
                                answerButtonWithBolt.click()
                            }

                            // Then handle any options with the page-correct-answer bolt
                            const optionsWithBolt = Array.from(
                                document.querySelectorAll(
                                    ".page-correct-answer"
                                )
                            ).map((bolt) => bolt.parentElement)
                            if (optionsWithBolt.length > 0) {
                                optionsWithBolt[0].click()

                                // After clicking an option, look for continue/next buttons
                                setTimeout(() => {
                                    const continueButtons = Array.from(
                                        document.querySelectorAll(
                                            'button, [role="button"]'
                                        )
                                    ).filter((button) => {
                                        const text = button.textContent
                                            .toLowerCase()
                                            .trim()
                                        return (
                                            text === "continue" ||
                                            text === "next" ||
                                            text.includes("continue") ||
                                            text.includes("next")
                                        )
                                    })
                                    if (continueButtons.length > 0) {
                                        continueButtons[0].click()
                                    }
                                    isProcessing = false // Reset processing flag
                                }, 500)
                            } else {
                                isProcessing = false // Reset processing flag if no options found
                            }
                        }, 0) // 1 second delay
                    }

                    // Check for Retry button
                    if (node.nodeType === 1) {
                        const retryButton = node.querySelector('button.sr_c2956596, button[class*="sr_"]');
                        if (retryButton) {
                            const retryDiv = retryButton.querySelector('div');
                            if (retryDiv && retryDiv.textContent === 'Retry') {
                                // Wait 2 seconds before clicking
                                setTimeout(() => {
                                    retryButton.click();
                                }, 2000);
                            }
                        }
                    }
                })
            }
        })
    })

    autoAnswerObserver.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true,
        attributes: true,
    })
}

function addAutoAnswerToggle() {
    // const settingsBar = document.querySelector(".settings-bar")
    // const autoAnswerToggle = document.createElement("div")
    // autoAnswerToggle.className = "modern-checkbox-container"
    // autoAnswerToggle.innerHTML = `
    //     <label class="modern-checkbox">
    //         <input type="checkbox" id="auto-answer-toggle">
    //         <span class="checkbox-box">
    //             <i class="fas fa-check checkbox-bolt"></i>
    //         </span>
    //         <span class="checkbox-label">Auto Answer</span>
    //     </label>
    // `
    // settingsBar.appendChild(autoAnswerToggle)

    const autoAnswerCheckbox = document.getElementById("auto-answer-toggle")
    autoAnswerCheckbox.addEventListener("change", (e) => {
        setupAutoAnswer(e.target.checked)
    })
}

async function sendToGemini(imageData) {
    const boltIcon = document.querySelector(".window-header .bolt-icon")
    const solution = document.getElementById("solution")
    const currentMode = document.querySelector(".menu-option.active").dataset
        .mode
    const answerOnlyMode =
        document.getElementById("answer-only-toggle")?.checked
    try {
        boltIcon.classList.add("loading")
        if (!imageData || !imageData.startsWith("data:image/")) {
            throw new Error("Invalid image data format")
        }
        const pageContent =
            currentMode === "reader" ? document.body.innerText : ""
        chrome.runtime.sendMessage(
            {
                action: "solveWithGemini",
                imageData: imageData,
                mode: currentMode,
                answerOnly: answerOnlyMode,
                pageContent: pageContent,
            },
            (response) => {
                if (chrome.runtime.lastError) {
                    console.error("Runtime error:", chrome.runtime.lastError)
                    throw new Error(
                        "Failed to communicate with background script"
                    )
                }
                if (response && response.error) {
                    throw new Error(response.error)
                }
            }
        )
    } catch (err) {
        console.error("Error getting solution:", err)
        boltIcon.classList.remove("loading")
        solution.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Could not solve the problem. Please try:</p>
                <ul>
                    <li>Taking a clearer screenshot</li>
                    <li>Making sure the content is clearly visible</li>
                    <li>Checking your internet connection</li>
                    <li>Stop SPAMMING the answer button</li>
                </ul>
            </div>
        `
    }
}

async function validateImageContent(imageData, mode) {
    try {
        const response = await chrome.runtime.sendMessage({
            action: "validateContent",
            imageData: imageData,
            mode: mode,
        })
        if (response.error) {
            return { isValid: !1, message: response.error }
        }
        const contentType = response.contentType
        switch (mode) {
            case "maths":
                if (!contentType.includes("mathematical")) {
                    return {
                        isValid: !1,
                        message:
                            "Mathematics mode only accepts math questions. Please switch to the appropriate mode for this content.",
                    }
                }
                break
            case "science":
                if (!contentType.includes("scientific")) {
                    if (contentType.includes("mathematical")) {
                        return {
                            isValid: !1,
                            message:
                                "Science mode only accepts science questions. For math questions, please use Mathematics mode.",
                        }
                    }
                    return {
                        isValid: !1,
                        message:
                            "Science mode only accepts science questions. Please switch to the appropriate mode for this content.",
                    }
                }
                break
            case "reader":
                if (contentType.includes("mathematical")) {
                    return {
                        isValid: !1,
                        message:
                            "Reader mode does not accept math questions. Please use Mathematics mode for solving math problems.",
                    }
                }
                if (contentType.includes("scientific")) {
                    return {
                        isValid: !1,
                        message:
                            "Reader mode does not accept science questions. Please use Science mode for scientific content.",
                    }
                }
                break
        }
        return { isValid: !0 }
    } catch (err) {
        console.error("Error validating content:", err)
        return { isValid: !0 }
    }
}

function autoFillAnswers(solutionText) {
    // Find all input fields with placeholder "Enter answer..."
    const answerInputs = Array.from(
        document.querySelectorAll('input[placeholder="Enter answer..."]')
    )

    if (answerInputs.length > 0) {
        // Split the solution text by common separators if multiple answers
        const answers = solutionText
            .split(/,\s*|\s+and\s+|\s*;\s*|\s*\)\s*|\s*\n\s*/)
            .map((answer) => {
                // Clean up the answer text
                return answer.replace(/^[a-z]\)\s*/i, "").trim()
            })

        // Fill each input field with corresponding answer
        answerInputs.forEach((input, index) => {
            if (answers[index]) {
                // Simulate real typing
                input.focus()
                input.value = answers[index]
                input.dispatchEvent(new Event("input", { bubbles: true }))
                input.dispatchEvent(new Event("change", { bubbles: true }))
            }
        })
    }
}

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "initialize" && validUser) {
        createFloatingWindow()
        if (request.mode === "reader") {
            // Wait for the window to be created and then activate reader mode
            setTimeout(() => {
                const readerOption = document.querySelector(
                    '.menu-option[data-mode="reader"]'
                )
                if (readerOption) {
                    readerOption.click()
                    // Show the copy page button since we're in reader mode
                    const copyPageBtn =
                        document.querySelector(".copy-page-button")
                    if (copyPageBtn) {
                        copyPageBtn.style.display = "block"
                    }
                }
            }, 100)
        }
    } else if (request.action === "solutionReady") {
        const boltIcon = document.querySelector(".window-header .bolt-icon")
        const solution = document.getElementById("solution")
        const answerOnlyMode =
            document.getElementById("answer-only-toggle")?.checked
        const currentMode = document.querySelector(".menu-option.active")
            .dataset.mode
        boltIcon.classList.remove("loading")
        let solutionText = request.solution
        solutionText = solutionText.replace(
            /\*\*(.*?)\*\*/g,
            "<strong>$1</strong>"
        )

        // Auto-fill answers in science mode
        if (currentMode === "science") {
            autoFillAnswers(solutionText)
        }

        if (!answerOnlyMode && solutionText.includes("\n")) {
            const steps = solutionText.split("\n").filter((step) => step.trim())
            solution.innerHTML = `
                <div class="solution-content">
                    <h3>Solution:</h3>
                    <div class="solution-steps">
                        ${steps.map((step, index) => `<div class="solution-step"><div class="step-number">${index + 1}</div><div class="step-content">${step}</div></div>`).join("")}
                    </div>
                </div>
            `
        } else {
            const copyButton =
                currentMode === "science"
                    ? `
                <button class="copy-answer-button" onclick="navigator.clipboard.writeText('${solutionText.replace(/'/g, "\\'")}')">
                    <i class="fas fa-copy"></i>
                </button>
            `
                    : ""

            solution.innerHTML = `
                <div class="solution-content ${answerOnlyMode ? "answer-only" : ""}">
                    <div class="solution-header">
                        <h3>${answerOnlyMode ? "Answer:" : "Solution:"}</h3>
                        ${copyButton}
                    </div>
                    <div class="solution-steps">
                        <div class="solution-step">
                            <div class="solution-text">
                                ${solutionText}
                            </div>
                        </div>
                    </div>
                </div>
            `
        }

        const pageOptions = document.querySelectorAll('button, [role="button"]')
        pageOptions.forEach((option) => {
            if (option.textContent.trim() === solutionText.trim()) {
                const bolt = document.createElement("i")
                bolt.className = "fas fa-check page-correct-answer"
                option.insertBefore(bolt, option.firstChild)
                bolt.style.marginRight = "8px"
            }
        })
    } else if (request.action === "solutionError") {
        const boltIcon = document.querySelector(".window-header .bolt-icon")
        const solution = document.getElementById("solution")
        boltIcon.classList.remove("loading")
        solution.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                <p>Error: ${request.error}</p>
                <ul>
                    <li>Please STOP SPAMMING the answer button</li>
                    <li>Check your internet connection</li>
                    <li>Make sure you're on the correct page</li>
                </ul>
            </div>
        `
    }
})

function escapeHtml(text) {
    const div = document.createElement("div")
    div.textContent = text
    return div.innerHTML
}
const style = document.createElement("style")
style.textContent = `
    .success-message {
        padding: 16px;
        background: rgba(45, 94, 255, 0.1);
        border: 1px solid rgba(45, 94, 255, 0.3);
        border-radius: 8px;
        color: #4e63ff;
        margin-top: 12px;
    }

    .success-message i {
        font-size: 24px;
        margin-bottom: 12px;
        display: block;
        color: #4e63ff;
    }

    .success-message p {
        margin: 0 0 12px 0;
        font-weight: 500;
        font-size: 14px;
        color: #fff;
    }

    .success-message ul {
        margin: 0;
        padding-left: 20px;
        color: rgba(255, 255, 255, 0.7);
    }

    .success-message li {
        margin: 6px 0;
        font-size: 13px;
    }
`

document.head.appendChild(style)
const additionalStyles = document.createElement("style")
additionalStyles.textContent = `
    .action-buttons {
        display: flex;
        gap: 8px;
    }

    .copy-page-button {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: #4e63ff;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(78, 99, 255, 0.3);
        transition: all 0.3s ease;
    }

    .copy-page-button i {
        font-size: 16px;
        color: white;
    }

    .copy-page-button:hover {
        transform: scale(1.05);
        background: #3b4ccc;
        box-shadow: 0 6px 16px rgba(78, 99, 255, 0.4);
    }

    .info-message {
        padding: 16px;
        background: rgba(45, 94, 255, 0.1);
        border: 1px solid rgba(45, 94, 255, 0.3);
        border-radius: 8px;
        color: #4e63ff;
        margin-top: 12px;
    }

    .info-message i {
        font-size: 24px;
        margin-bottom: 12px;
        display: block;
        color: #4e63ff;
    }

    .info-message p {
        margin: 0 0 12px 0;
        font-weight: 500;
        font-size: 14px;
        color: #fff;
    }

    .info-message ul {
        margin: 0;
        padding-left: 20px;
        color: rgba(255, 255, 255, 0.7);
    }

    .info-message li {
        margin: 6px 0;
        font-size: 13px;
    }

    /* Lightning bolt animation */
    .bolt-icon.loading {
        animation: spin 2s linear infinite;
        position: relative;
        color: #4e63ff;
    }

    .bolt-icon.loading::before,
    .bolt-icon.loading::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: rgba(78, 99, 255, 0.4);
        animation: spark 1.5s ease-out infinite;
    }

    .bolt-icon.loading::after {
        animation-delay: 0.5s;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes spark {
        0% {
            transform: scale(1);
            opacity: 0.8;
        }
        100% {
            transform: scale(2);
            opacity: 0;
        }
    }

    /* Answer-only mode styles */
    .answer-only .solution-content {
        color: #4e63ff !important;
    }

    .answer-only .solution-step {
        background: rgba(78, 99, 255, 0.1);
        border: 1px solid rgba(78, 99, 255, 0.3);
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 12px;
    }

    .answer-only .solution-text {
        color: #4e63ff !important;
        font-weight: 500;
    }

    .answer-only strong {
        color: #2d5eff;
        font-weight: 600;
    }
`

document.head.appendChild(additionalStyles)
const loadingStyles = document.createElement("style")
loadingStyles.textContent = `
    .bolt-icon {
        display: inline-block;
        transition: transform 0.3s ease;
    }

    .bolt-icon.loading {
        animation: spin 3s linear infinite;
        color: #4e63ff;
    }

    .bolt-icon.loading::before,
    .bolt-icon.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 150%;
        height: 150%;
        border-radius: 50%;
        background: rgba(78, 99, 255, 0.4);
        transform: translate(-50%, -50%) scale(1);
        animation: spark 2s ease-out infinite;
        pointer-events: none;
    }

    .bolt-icon.loading::after {
        animation-delay: 1s;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes spark {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.8;
        }
        100% {
            transform: translate(-50%, -50%) scale(2.5);
            opacity: 0;
        }
    }

    /* Answer-only mode styles */
    .answer-only .solution-content {
        color: #4e63ff !important;
    }

    .answer-only .solution-step {
        background: rgba(78, 99, 255, 0.1);
        border: 1px solid rgba(78, 99, 255, 0.3);
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 12px;
    }

    .answer-only .solution-text {
        color: #4e63ff !important;
        font-weight: 500;
        font-size: 16px;
    }
`

document.head.appendChild(loadingStyles)
const answerStyles = document.createElement("style")
answerStyles.textContent = `
    .answer-only .solution-content {
        color: #4e63ff !important;
    }

    .answer-only .solution-step {
        background: rgba(78, 99, 255, 0.1);
        border: 1px solid rgba(78, 99, 255, 0.3);
        padding: 16px;
        border-radius: 8px;
        margin-bottom: 12px;
    }

    .answer-only .solution-text {
        color: #4e63ff !important;
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5;
    }

    .answer-only h3 {
        color: #4e63ff;
        margin-bottom: 12px;
        font-size: 18px;
    }

    .answer-only strong {
        color: #2d5eff;
        font-weight: 600;
    }
`

document.head.appendChild(answerStyles)
const boltStyles = document.createElement("style")
boltStyles.textContent = `
    .correct-answer-bolt {
        color: #4ade80;
        margin-right: 8px;
        animation: glow 2s ease-in-out infinite;
    }

    @keyframes glow {
        0%, 100% {
            filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.6));
        }
        50% {
            filter: drop-shadow(0 0 6px rgba(74, 222, 128, 0.8));
        }
    }

    .solution-text {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .answer-only .correct-answer-bolt {
        font-size: 18px;
    }

    /* Existing bolt animation styles */
    .bolt-icon.loading {
        animation: spin 3s linear infinite;
        color: #4e63ff;
    }

    .bolt-icon.loading::before,
    .bolt-icon.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 150%;
        height: 150%;
        border-radius: 50%;
        background: rgba(78, 99, 255, 0.4);
        transform: translate(-50%, -50%) scale(1);
        animation: spark 2s ease-out infinite;
        pointer-events: none;
    }

    .bolt-icon.loading::after {
        animation-delay: 1s;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes spark {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.8;
        }
        100% {
            transform: translate(-50%, -50%) scale(2.5);
            opacity: 0;
        }
    }
`

document.head.appendChild(boltStyles)
const pageAnswerStyles = document.createElement("style")
pageAnswerStyles.textContent = `
    .page-correct-answer {
        color:rgb(74, 126, 222);
        animation: pageBoltGlow 2s ease-in-out infinite;
        margin-right: 8px;
        font-size: 16px;
    }

    @keyframes pageBoltGlow {
        0%, 100% {
            filter: drop-shadow(0 0 2px rgba(126, 74, 222, 0.6));
        }
        50% {
            filter: drop-shadow(0 0 6px rgba(74, 168, 222, 0.8));
        }
    }
`

document.head.appendChild(pageAnswerStyles)
const solutionStyles = document.createElement("style")
solutionStyles.textContent = `
    .window-content {
        max-height: 80vh;
        overflow-y: auto;
        padding-right: 8px;
        background: url('background.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        backdrop-filter: blur(10px);
    }

    /* Modern Scrollbar */
    .window-content::-webkit-scrollbar {
        width: 6px;
    }

    .window-content::-webkit-scrollbar-track {
        background: rgba(78, 99, 255, 0.1);
        border-radius: 3px;
    }

    .window-content::-webkit-scrollbar-thumb {
        background: #4e63ff;
        border-radius: 3px;
        transition: all 0.3s ease;
    }

    .window-content::-webkit-scrollbar-thumb:hover {
        background: #3b4ccc;
    }

    /* Solution Content Styles */
    .solution-content {
        padding: 20px;
        background: rgba(45, 94, 255, 0.08);
        backdrop-filter: blur(12px);
        border-radius: 12px;
        border: 1px solid rgba(78, 99, 255, 0.2);
        box-shadow: 0 4px 24px rgba(45, 94, 255, 0.1);
    }

    .solution-steps {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .solution-step {
        background: rgba(45, 94, 255, 0.1);
        backdrop-filter: blur(8px);
        border: 1px solid rgba(78, 99, 255, 0.15);
        padding: 20px;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .solution-step:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(45, 94, 255, 0.15);
    }

    .step-number {
        background: #4e63ff;
        color: white;
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 600;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(78, 99, 255, 0.3);
    }

    .step-content {
        color: rgba(255, 255, 255, 0.95);
        font-size: 15px;
        line-height: 1.6;
        flex-grow: 1;
    }

    .solution-content h3 {
        color: #4e63ff;
        margin: 0 0 20px 0;
        font-size: 18px;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(78, 99, 255, 0.2);
    }

    /* Fixed Bolt Animation */
    .window-header .bolt-icon {
        display: inline-block !important;
        color: #4e63ff;
        font-size: 18px;
        margin-right: 8px;
        position: relative;
        z-index: 10;
    }

    .window-header .bolt-icon.loading {
        animation: spin 2s linear infinite;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .window-header .bolt-icon.loading::before,
    .window-header .bolt-icon.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 200%;
        height: 200%;
        border-radius: 50%;
        background: rgba(78, 99, 255, 0.4);
        transform: translate(-50%, -50%) scale(1);
        animation: spark 2s ease-out infinite;
        pointer-events: none;
    }

    .window-header .bolt-icon.loading::after {
        animation-delay: 1s;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes spark {
        0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.8;
        }
        100% {
            transform: translate(-50%, -50%) scale(2.5);
            opacity: 0;
        }
    }

    /* Answer Only Mode */
    .answer-only .solution-step {
        background: rgba(78, 99, 255, 0.1);
        border: 1px solid rgba(78, 99, 255, 0.3);
    }

    .answer-only .solution-text {
        color: #4e63ff !important;
        font-weight: 500;
        font-size: 16px;
    }

    /* Glow Effects */
    .solution-steps::after {
        content: '';
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 80%;
        height: 40px;
        background: radial-gradient(ellipse at center, rgba(78, 99, 255, 0.2) 0%, rgba(78, 99, 255, 0) 70%);
        pointer-events: none;
        z-index: -1;
    }
`

const existingSolutionStyles = document.querySelector("#solution-styles")
if (existingSolutionStyles) {
    existingSolutionStyles.remove()
}
solutionStyles.id = "solution-styles"
document.head.appendChild(solutionStyles)
const answerBoltStyles = document.createElement("style")
answerBoltStyles.textContent = `
    .answer-bolt {
        color:rgb(74, 121, 222);
        margin-right: 8px;
        animation: answerBoltGlow 2s ease-in-out infinite;
        cursor: pointer;
    }

    @keyframes answerBoltGlow {
        0%, 100% {
            filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.6));
        }
        50% {
            filter: drop-shadow(0 0 6px rgba(74, 222, 128, 0.8));
        }
    }
`

document.head.appendChild(answerBoltStyles)
const checkboxStyles = document.createElement("style")
checkboxStyles.textContent = `
    .modern-checkbox-container {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .modern-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        user-select: none;
    }

    .checkbox-box {
        width: 24px;
        height: 24px;
        border: 2px solid rgba(78, 99, 255, 0.5);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        background: rgba(78, 99, 255, 0.1);
        position: relative;
        overflow: hidden;
    }

    .checkbox-bolt {
        color: #ff4e4e;
        font-size: 14px;
        transform: scale(0) rotate(-180deg);
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        opacity: 0;
    }

    .modern-checkbox input[type="checkbox"] {
        display: none;
    }

    .modern-checkbox input[type="checkbox"]:checked + .checkbox-box {
        border-color: rgba(78, 99, 255, 0.8);
        background: rgba(78, 99, 255, 0.2);
    }

    .modern-checkbox input[type="checkbox"]:checked + .checkbox-box .checkbox-bolt {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }

    .checkbox-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        font-weight: 500;
    }

    .modern-checkbox:hover .checkbox-box {
        border-color: rgba(78, 99, 255, 0.8);
        box-shadow: 0 0 12px rgba(78, 99, 255, 0.3);
    }
`

document.head.appendChild(checkboxStyles)
function handleReadingFlow() {
    // 1. Find "I have read up to here" button (exact match)
    const readButton = Array.from(document.querySelectorAll("button")).find(
        (button) => {
            const text = button.textContent.trim() // Case-sensitive check
            return text === "I have read up to here"
        }
    )

    if (readButton) {
        // Add visual bolt icon
        if (!readButton.querySelector(".fa-check")) {
            const bolt = document.createElement("i")
            bolt.className = "fas fa-check"
            bolt.style.cssText = `
                color:rgb(74, 133, 222);
                margin-right: 8px;
                animation: pageBoltGlow 2s ease-in-out infinite;
                font-size: 16px;
            `
            readButton.insertBefore(bolt, readButton.firstChild)
        }

        // Click the read button
        readButton.click()

        // 2. After 1 second, check for "Yes, asks me the questions" (exact match)
        setTimeout(() => {
            const yesButton = Array.from(
                document.querySelectorAll("button")
            ).find((button) => {
                const text = button.textContent.trim()
                return text === "Yes, asks me the questions" // Exact match
            })

            if (yesButton) {
                // Add bolt icon and click
                if (!yesButton.querySelector(".fa-check")) {
                    const bolt = document.createElement("i")
                    bolt.className = "fas fa-check"
                    bolt.style.cssText = `
                        color:rgb(74, 76, 222);
                        margin-right: 8px;
                        animation: pageBoltGlow 2s ease-in-out infinite;
                        font-size: 16px;
                    `
                    yesButton.insertBefore(bolt, yesButton.firstChild)
                }
                yesButton.click()
            }
            // If no matching button found, do nothing
        }, 0) // Wait 1 second for popup to appear
    }
}

function createBookIcon() {
    const bookIcon = document.createElement("button")
    bookIcon.className = "floating-book-icon"
    bookIcon.innerHTML = '<i class="fas fa-book"></i>'
    bookIcon.title = "Click to mark as read"
    bookIcon.addEventListener("click", handleReadingFlow)
    document.body.appendChild(bookIcon)
    if (!document.querySelector("#book-icon-styles")) {
        const bookIconStyles = document.createElement("style")
        bookIconStyles.id = "book-icon-styles"
        bookIconStyles.textContent = `
            .floating-book-icon {
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                border-radius: 25px;
                background: rgba(78, 99, 255, 0.2);
                border: 2px solid rgba(78, 99, 255, 0.5);
                color: #4e63ff;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 10000;
                transition: all 0.3s ease;
                box-shadow: 0 2px 10px rgba(78, 99, 255, 0.2);
            }
            
            .floating-book-icon:hover {
                background: rgba(78, 99, 255, 0.3);
                border-color: rgba(78, 99, 255, 0.8);
                box-shadow: 0 2px 15px rgba(78, 99, 255, 0.4);
                transform: translateY(-2px);
            }
            
            .floating-book-icon i {
                font-size: 20px;
            }
        `
        document.head.appendChild(bookIconStyles)
    }
}
const styles = `
    .solution-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        color: #4171ff;
    }

    .solution-loading i {
        font-size: 24px;
        animation: spin 1s linear infinite;
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-block !important;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .fa-check {
        opacity: 1 !important;
        visibility: visible !important;
        display: inline-block !important;
    }

    .loading .fa-check {
        animation: spin 1s linear infinite;
        color: #4171ff !important;
    }

    #answer-container.loading .fa-check {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #4171ff !important;
        animation: spin 1s linear infinite;
    }
`

const styleSheet = document.createElement("style")
styleSheet.textContent = styles
document.head.appendChild(styleSheet)
function showLoading() {
    const answerContainer = document.getElementById("answer-container")
    if (answerContainer) {
        answerContainer.innerHTML = `
            <div class="solution-loading">
                <i class="fas fa-multiply" style="color: #4171ff !important; display: inline-block !important; visibility: visible !important;"></i>
                <div class="loading-text">Processing...</div>
            </div>
        `
        answerContainer.classList.add("loading")
    }
}
function hideLoading() {
    const answerContainer = document.getElementById("answer-container")
    if (answerContainer) {
        answerContainer.classList.remove("loading")
    }
}
function displayAnswer(answer) {
    const answerContainer = document.getElementById("answer-container")
    if (answerContainer) {
        hideLoading()
        answerContainer.innerHTML = `
            <div class="solution-content">
                <i class="fas fa-check"></i>
                <div class="solution-text">${answer}</div>
            </div>
        `
    }
}
const loadingStylesUpdate = `
    .solution-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20px;
        gap: 10px;
    }

    .solution-loading i.fa-check {
        font-size: 24px;
        color: #4171ff !important;
        animation: spin 2s linear infinite;
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .solution-loading .loading-text {
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
        margin-top: 8px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Override any other styles that might hide the bolt */
    .loading .fa-check,
    #answer-container.loading .fa-check,
    .solution-loading .fa-check {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
        color: #4171ff !important;
    }
`
const loadingStyleSheet = document.createElement("style")
loadingStyleSheet.textContent = loadingStylesUpdate
document.head.appendChild(loadingStyleSheet)

function handleContinueReadingButton() {
    const buttons = Array.from(document.querySelectorAll("button")).filter(
        (button) => {
            const text = button.textContent.toLowerCase().trim()
            return text === "continue reading" || text === "next"
        }
    )

    buttons.forEach((button) => {
        // Only add bolt if it doesn't already exist
        if (!button.querySelector(".fa-check")) {
            const bolt = document.createElement("i")
            bolt.className = "fas fa-check"
            bolt.style.cssText = `
                color:rgb(74, 133, 222);
                margin-right: 8px;
                animation: pageBoltGlow 2s ease-in-out infinite;
                font-size: 16px;
            `
            button.insertBefore(bolt, button.firstChild)
            button.click() // Automatically click the button
        }
    })
}

// Add observer to handle dynamically added buttons
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
            handleContinueReadingButton()
        }
    })
})

observer.observe(document.body, {
    childList: true,
    subtree: true,
})

// Initial check for buttons
handleContinueReadingButton()

// Add styles for the copy button
const copyButtonStyles = document.createElement("style")
copyButtonStyles.textContent = `
    .solution-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .copy-answer-button {
        background: rgba(78, 99, 255, 0.2);
        border: 1px solid rgba(78, 99, 255, 0.3);
        color: #4e63ff;
        padding: 8px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .copy-answer-button:hover {
        background: rgba(78, 99, 255, 0.3);
        transform: translateY(-1px);
    }

    .copy-answer-button i {
        font-size: 14px;
    }

    .copy-answer-button:active {
        transform: translateY(1px);
    }
`
document.head.appendChild(copyButtonStyles)

const titleWaveStyles = document.createElement("style")
titleWaveStyles.textContent = `
    .window-title {
        font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        font-weight: 800;
        font-size: 18px;
        background: linear-gradient(
            45deg,
            rgb(74, 133, 222) 0%,
            rgb(74, 168, 222) 29%,
            rgb(74, 121, 222) 67%,
            rgb(74, 76, 222) 100%
        );
        background-size: 300% 300%;
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: wave 8s ease infinite;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    @keyframes wave {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
`
document.head.appendChild(titleWaveStyles)
