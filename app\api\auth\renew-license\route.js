import { NextResponse } from 'next/server';
import { getAuthManager } from '../../../../lib/auth';
import { getDatabase } from '../../../../lib/database';

export async function POST(request) {
  try {
    const auth = getAuthManager();
    const db = getDatabase();
    
    // Get token from header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    try {
      // Validate session
      const session = auth.validateSession(token);
      
      // Get the new license key from request body
      const { newLicenseKey } = await request.json();
      
      if (!newLicenseKey) {
        return NextResponse.json(
          { success: false, error: 'New license key is required' },
          { status: 400 }
        );
      }

      // Admins cannot renew licenses (they don't have license restrictions)
      if (session.role === 'admin') {
        return NextResponse.json(
          { success: false, error: 'Admins do not need license renewal' },
          { status: 400 }
        );
      }

      // Get client IP for logging
      const clientIP = auth.getClientIP(request);

      try {
        // Attempt to renew the license
        const renewalResult = db.renewUserLicense(session.userId, newLicenseKey);
        
        // Log successful renewal
        db.logActivity(
          session.userId, 
          'LICENSE_RENEWAL_SUCCESS', 
          `License successfully renewed with key: ${newLicenseKey}`, 
          clientIP
        );

        return NextResponse.json({
          success: true,
          message: 'License renewed successfully',
          licenseInfo: {
            expiresAt: renewalResult.expiresAt,
            maxUses: renewalResult.maxUses,
            currentUses: renewalResult.currentUses
          }
        });

      } catch (renewalError) {
        // Log failed renewal attempt
        db.logActivity(
          session.userId, 
          'LICENSE_RENEWAL_FAILED', 
          `Failed license renewal attempt with key: ${newLicenseKey} - ${renewalError.message}`, 
          clientIP
        );
        
        return NextResponse.json(
          { success: false, error: renewalError.message },
          { status: 400 }
        );
      }

    } catch (authError) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('License renewal error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}