import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getAuthManager } from '../../../../lib/auth';

export async function POST(request) {
  try {
    const { username, password, licenseKey } = await request.json();

    // Validate input
    if (!username || !password || !licenseKey) {
      return NextResponse.json(
        { success: false, error: 'Username, password, and license key are required' },
        { status: 400 }
      );
    }

    const db = getDatabase();
    const auth = getAuthManager();

    // Get client IP for rate limiting and logging
    const clientIP = auth.getClientIP(request);

    // Check rate limiting
    const rateLimit = auth.checkRateLimit(`register_${clientIP}`, 3, 60); // 3 attempts per hour
    if (!rateLimit.allowed) {
      const resetTime = new Date(rateLimit.resetTime);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Too many registration attempts. Please try again later.',
          resetTime: resetTime.toISOString()
        },
        { status: 429 }
      );
    }

    // Validate password strength
    const passwordValidation = auth.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Password does not meet requirements',
          details: passwordValidation.errors
        },
        { status: 400 }
      );
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Username must be 3-20 characters long and contain only letters, numbers, and underscores'
        },
        { status: 400 }
      );
    }

    try {
      // Create user
      const userId = db.createUser(username, password, licenseKey);
      
      // Log successful registration
      db.logActivity(userId, 'REGISTER', `User registered with license key: ${licenseKey}`, clientIP);

      return NextResponse.json({
        success: true,
        message: 'Account created successfully. Please login with your credentials.',
        userId
      });

    } catch (dbError) {
      // Log failed registration attempt
      db.logActivity(null, 'REGISTER_FAILED', `Failed registration attempt for username: ${username}, license: ${licenseKey}, error: ${dbError.message}`, clientIP);
      
      return NextResponse.json(
        { success: false, error: dbError.message },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}