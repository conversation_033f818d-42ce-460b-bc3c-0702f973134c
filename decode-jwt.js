function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }
    
    const header = JSON.parse(Buffer.from(parts[0], 'base64').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64').toString());
    
    return { header, payload };
  } catch (error) {
    console.error('Error decoding JWT:', error.message);
    return null;
  }
}

// Test with the failing token from the logs
const failingToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEwLCJ1c2VybmFtZSI6InF1ZXVldGVzdF8xNzUxNzY3MTQxODMwIiwicm9sZSI6InVzZXIiLCJpYXQiOjE3NTE3Njc4MzEsImV4cCI6MTc1MjM3MjYzMX0.XUGz1VseNq_swenKFAs3JclScI9vM_HWeilqK-Jr8b4';

const workingToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjksInVzZXJuYW1lIjoiQ29kZU1hc3Rlcl8wMSIsInJvbGUiOiJ1c2VyIiwiaWF0IjoxNzUxNzYyODU4LCJleHAiOjE3NTIzNjc2NTh9.4-ttQlumPDdBYjbZiZ5NnuEbBuO4Zqd9JaCBKi1CTD4';

console.log('🔍 Decoding failing token (queuetest user):');
const failingDecoded = decodeJWT(failingToken);
if (failingDecoded) {
  console.log('Header:', failingDecoded.header);
  console.log('Payload:', failingDecoded.payload);
  console.log('Expires at:', new Date(failingDecoded.payload.exp * 1000));
  console.log('Current time:', new Date());
  console.log('Is expired?', Date.now() > failingDecoded.payload.exp * 1000);
}

console.log('\n🔍 Decoding working token (CodeMaster_01):');
const workingDecoded = decodeJWT(workingToken);
if (workingDecoded) {
  console.log('Header:', workingDecoded.header);
  console.log('Payload:', workingDecoded.payload);
  console.log('Expires at:', new Date(workingDecoded.payload.exp * 1000));
  console.log('Current time:', new Date());
  console.log('Is expired?', Date.now() > workingDecoded.payload.exp * 1000);
}
