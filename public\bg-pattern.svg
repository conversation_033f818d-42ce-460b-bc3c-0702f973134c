<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255, 255, 255, 0.07)" stroke-width="0.5"/>
    </pattern>
  </defs>
  <rect width="100%" height="100%" fill="url(#grid)" />
  <circle cx="50" cy="50" r="2" fill="rgba(255, 255, 255, 0.1)" />
  <circle cx="10" cy="10" r="1" fill="rgba(255, 255, 255, 0.1)" />
  <circle cx="90" cy="10" r="1" fill="rgba(255, 255, 255, 0.1)" />
  <circle cx="10" cy="90" r="1" fill="rgba(255, 255, 255, 0.1)" />
  <circle cx="90" cy="90" r="1" fill="rgba(255, 255, 255, 0.1)" />
</svg> 