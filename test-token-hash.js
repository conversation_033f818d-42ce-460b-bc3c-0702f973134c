// Load environment variables
require('dotenv').config();

const { getAuthManager } = require('./lib/auth');
const { getDatabase } = require('./lib/database');

async function testTokenHash() {
  console.log('🔐 Testing Token Hash Calculation...\n');
  
  try {
    const auth = getAuthManager();
    const db = getDatabase();
    
    // The failing token from the logs
    const failingToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEwLCJ1c2VybmFtZSI6InF1ZXVldGVzdF8xNzUxNzY3MTQxODMwIiwicm9sZSI6InVzZXIiLCJpYXQiOjE3NTE3Njc4MzEsImV4cCI6MTc1MjM3MjYzMX0.XUGz1VseNq_swenKFAs3JclScI9vM_HWeilqK-Jr8b4';
    
    console.log('Testing failing token:');
    console.log(`Token: ${failingToken.substring(0, 50)}...`);
    
    // Calculate the hash
    const tokenHash = auth.hashToken(failingToken);
    console.log(`Token hash: ${tokenHash.substring(0, 20)}...`);
    
    // Check if this hash exists in the database
    const session = db.db.prepare(`
      SELECT s.*, u.username 
      FROM user_sessions s 
      JOIN users u ON s.user_id = u.id 
      WHERE s.token_hash = ?
    `).get(tokenHash);
    
    if (session) {
      console.log('✅ Session found in database:');
      console.log(`   User: ${session.username} (ID: ${session.user_id})`);
      console.log(`   Session ID: ${session.id}`);
      console.log(`   Is Active: ${session.is_active}`);
      console.log(`   Expires: ${session.expires_at}`);
      console.log(`   Current time: ${new Date().toISOString()}`);
      
      // Check if expired
      const isExpired = new Date(session.expires_at) < new Date();
      console.log(`   Is Expired: ${isExpired}`);
      
      // Test the database validation directly
      console.log('\n🧪 Testing database validation directly:');
      const dbValidation = db.validateSession(tokenHash);
      if (dbValidation) {
        console.log('✅ Database validation successful:', dbValidation);
      } else {
        console.log('❌ Database validation failed');
      }
      
    } else {
      console.log('❌ Session not found in database');
      
      // Show recent sessions for this user
      console.log('\nRecent sessions for user ID 10:');
      const recentSessions = db.db.prepare(`
        SELECT s.*, u.username 
        FROM user_sessions s 
        JOIN users u ON s.user_id = u.id 
        WHERE s.user_id = 10
        ORDER BY s.created_at DESC 
        LIMIT 5
      `).all();
      
      recentSessions.forEach((s, i) => {
        console.log(`${i + 1}. Hash: ${s.token_hash.substring(0, 20)}... (Active: ${s.is_active})`);
      });
    }
    
    // Test the full auth validation
    console.log('\n🔍 Testing full auth validation:');
    try {
      const authResult = auth.validateSession(failingToken);
      console.log('✅ Auth validation successful:', authResult);
    } catch (error) {
      console.log('❌ Auth validation failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testTokenHash();
}

module.exports = { testTokenHash };
