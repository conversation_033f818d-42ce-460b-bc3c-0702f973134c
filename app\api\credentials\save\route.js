import { NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';
import { getAuthManager } from '../../../../lib/auth';

export async function POST(request) {
  try {
    const { loginMethod, school, email, password } = await request.json();

    // Validate input
    if (!loginMethod || !school || !email || !password) {
      return NextResponse.json(
        { success: false, error: 'Login method, school, email, and password are required' },
        { status: 400 }
      );
    }

    if (!['normal', 'microsoft', 'google'].includes(loginMethod)) {
      return NextResponse.json(
        { success: false, error: 'Invalid login method' },
        { status: 400 }
      );
    }

    const auth = getAuthManager();
    
    // Check authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const session = auth.validateSession(token);

    const db = getDatabase();
    
    // Generate encryption key based on user session
    const encryptionKey = `${session.userId}-${process.env.JWT_SECRET || 'default-key'}`;
    
    // Save encrypted credentials
    const loginKey = db.saveEncryptedCredentials(
      session.userId, 
      loginMethod, 
      school,
      email, 
      password, 
      encryptionKey
    );

    // Log activity
    db.logActivity(session.userId, 'CREDENTIALS_SAVED', `Saved ${loginMethod} login credentials`);

    return NextResponse.json({
      success: true,
      loginKey,
      message: 'Credentials saved successfully'
    });

  } catch (error) {
    console.error('Save credentials error:', error);
    return NextResponse.json(
      { success: false, error: error.message === 'Invalid session' ? 'Authentication required' : 'Internal server error' },
      { status: error.message === 'Invalid session' ? 401 : 500 }
    );
  }
}