// Test script to verify queue system functionality
const { getDatabase } = require('./lib/database');
const { getWebhookManager } = require('./lib/webhook');
const { getQueueProcessor } = require('./lib/queueProcessor');

async function testQueueSystem() {
  console.log('🚀 Testing Queue System Functionality...\n');
  
  const db = getDatabase();
  const webhook = getWebhookManager();
  const processor = getQueueProcessor();
  
  try {
    // 1. Create test license with queue features
    console.log('1. Creating test license with queue features...');
    const testLicense = db.createLicenseKey(30, 5, [], null);
    console.log(`   Created license: ${testLicense.keyCode}`);
    
    // Set queue features for the license
    db.setLicenseFeatures(testLicense.id, {
      max_accounts_per_batch: 10,
      priority_level: 5,
      scheduling_access: true
    });
    console.log('   ✅ Queue features configured');

    // 2. Create test user with the license
    console.log('2. Creating test user...');
    const testUser = 'queuetest_' + Date.now();
    const userId = db.createUser(testUser, 'password123', testLicense.keyCode);
    console.log(`   Created user: ${testUser} (ID: ${userId})`);

    // 3. Test license feature retrieval
    console.log('3. Testing license feature retrieval...');
    const userFeatures = db.getUserLicenseFeatures(userId);
    console.log(`   Max accounts per batch: ${userFeatures.max_accounts_per_batch}`);
    console.log(`   Priority level: ${userFeatures.priority_level}`);
    console.log(`   Scheduling access: ${userFeatures.scheduling_access}`);
    console.log('   ✅ License features retrieved successfully');

    // 4. Test batch creation
    console.log('4. Testing batch creation...');
    const testAccounts = [
      { school: 'Test School 1', email: '<EMAIL>', password: 'pass123' },
      { school: 'Test School 2', email: '<EMAIL>', password: 'pass456' },
      { school: 'Test School 3', email: '<EMAIL>', password: 'pass789' }
    ];
    
    const batchId = db.createQueueBatch(userId, 'Test Batch', testAccounts);
    console.log(`   Created batch ID: ${batchId}`);
    
    const batchJobs = db.getBatchJobs(batchId);
    console.log(`   Created ${batchJobs.length} jobs in batch`);
    console.log('   ✅ Batch creation successful');

    // 5. Test scheduled batch creation
    console.log('5. Testing scheduled batch creation...');
    const scheduledTime = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
    const scheduledBatchId = db.createQueueBatch(
      userId, 
      'Scheduled Test Batch', 
      testAccounts.slice(0, 2), 
      scheduledTime.toISOString()
    );
    console.log(`   Created scheduled batch ID: ${scheduledBatchId}`);
    console.log(`   Scheduled for: ${scheduledTime.toLocaleString()}`);
    console.log('   ✅ Scheduled batch creation successful');

    // 6. Test schedule conflict detection
    console.log('6. Testing schedule conflict detection...');
    try {
      const conflictTime = new Date(scheduledTime.getTime() + 15 * 60 * 1000); // 15 minutes later
      const conflicts = db.checkScheduleConflicts(userId, conflictTime.toISOString(), 30);
      console.log(`   Found ${conflicts.length} conflicts (expected: 1)`);
      console.log('   ✅ Schedule conflict detection working');
    } catch (error) {
      console.log(`   ⚠️  Schedule conflict test failed: ${error.message}`);
    }

    // 7. Test priority management
    console.log('7. Testing priority management...');
    const jobs = db.getBatchJobs(batchId);
    const firstJob = jobs[0];
    
    console.log(`   Original priority: ${firstJob.effective_priority}`);
    db.updateJobPriority(firstJob.id, 8, true); // Admin override
    
    const updatedJob = db.db.prepare('SELECT * FROM queue_jobs WHERE id = ?').get(firstJob.id);
    console.log(`   Updated priority: ${updatedJob.effective_priority}`);
    console.log('   ✅ Priority management working');

    // 8. Test queue statistics
    console.log('8. Testing queue statistics...');
    const queueStats = db.getQueueStats();
    console.log(`   Jobs by status:`, queueStats.jobsByStatus);
    console.log(`   Jobs by priority:`, queueStats.jobsByPriority);
    console.log(`   Average wait time: ${Math.round(queueStats.averageWaitTime)} minutes`);
    console.log('   ✅ Queue statistics working');

    // 9. Test webhook notifications
    console.log('9. Testing webhook notifications...');
    await webhook.sendBatchCreated(testUser, 'Test Batch', testAccounts.length);
    await webhook.sendLicenseViolation(testUser, 'Test Violation', 'This is a test violation');
    await webhook.sendPriorityAdjustment(firstJob.id, 5, 8, 'Test priority adjustment', 'admin');
    console.log('   ✅ Webhook notifications sent');

    // 10. Test queue processor (brief test)
    console.log('10. Testing queue processor...');
    console.log(`   Processor status: ${processor.getStatus().is_processing ? 'Running' : 'Stopped'}`);
    
    // Start processor briefly to test
    processor.start();
    console.log('   Started processor...');
    
    // Wait a moment then stop
    await new Promise(resolve => setTimeout(resolve, 2000));
    processor.stop();
    console.log('   Stopped processor');
    console.log('   ✅ Queue processor test completed');

    // 11. Test starvation prevention
    console.log('11. Testing starvation prevention...');
    const starvationResult = db.applyStarvationPrevention();
    console.log(`   Applied starvation prevention to ${starvationResult.changes} jobs`);
    console.log('   ✅ Starvation prevention working');

    // 12. Test batch status updates
    console.log('12. Testing batch status updates...');
    db.updateBatchStatus(batchId, 'processing');
    const updatedBatch = db.getQueueBatches(userId, null, 1, 0)[0];
    console.log(`   Batch status updated to: ${updatedBatch.status}`);
    console.log('   ✅ Batch status updates working');

    // 13. Test user schedules
    console.log('13. Testing user schedules...');
    const userSchedules = db.getUserSchedules(userId);
    console.log(`   Found ${userSchedules.length} schedules for user`);
    userSchedules.forEach(schedule => {
      console.log(`   - Schedule ${schedule.id}: ${new Date(schedule.scheduled_time).toLocaleString()}`);
    });
    console.log('   ✅ User schedules working');

    // 14. Test license feature validation
    console.log('14. Testing license feature validation...');
    try {
      // Test batch size limit
      const largeBatch = Array(15).fill().map((_, i) => ({
        school: `School ${i}`,
        email: `test${i}@example.com`,
        password: 'password'
      }));
      
      db.createQueueBatch(userId, 'Large Batch', largeBatch);
      console.log('   ⚠️  Batch size limit not enforced (this should have failed)');
    } catch (error) {
      console.log(`   ✅ Batch size limit enforced: ${error.message}`);
    }

    console.log('\n🎉 All queue system tests completed successfully!');
    console.log('\nTest Summary:');
    console.log(`   License Key: ${testLicense.keyCode}`);
    console.log(`   Test User: ${testUser}`);
    console.log(`   Batches Created: 2`);
    console.log(`   Jobs Created: ${testAccounts.length + 2}`);
    console.log(`   Schedules Created: 1`);
    
    // Cleanup option
    console.log('\nTest data created. You can manually clean up or leave for further testing.');
    
  } catch (error) {
    console.error('❌ Queue system test failed:', error.message);
    console.error(error.stack);
  }
}

// Additional utility functions for testing
async function testWebhookConnectivity() {
  console.log('🔗 Testing webhook connectivity...');
  
  try {
    const webhook = getWebhookManager();
    await webhook.sendQueueAlert(
      'info',
      'Queue system test notification',
      {
        test_timestamp: new Date().toISOString(),
        system_status: 'operational'
      }
    );
    console.log('✅ Webhook connectivity test passed');
  } catch (error) {
    console.error('❌ Webhook connectivity test failed:', error.message);
  }
}

async function testDatabasePerformance() {
  console.log('⚡ Testing database performance...');
  
  const db = getDatabase();
  const startTime = Date.now();
  
  // Test bulk operations
  const testOperations = [
    () => db.getQueueStats(),
    () => db.getLicenseKeys(50, 0),
    () => db.getActivityLogs(null, 100, 0),
    () => db.getSystemStats()
  ];
  
  for (const operation of testOperations) {
    const opStart = Date.now();
    operation();
    const opTime = Date.now() - opStart;
    console.log(`   Operation completed in ${opTime}ms`);
  }
  
  const totalTime = Date.now() - startTime;
  console.log(`✅ Database performance test completed in ${totalTime}ms`);
}

// Run tests based on command line arguments
const args = process.argv.slice(2);

if (args.includes('--webhook-only')) {
  testWebhookConnectivity().then(() => process.exit(0));
} else if (args.includes('--performance-only')) {
  testDatabasePerformance().then(() => process.exit(0));
} else {
  // Run full test suite
  testQueueSystem().then(() => {
    console.log('\nRunning additional tests...');
    return Promise.all([
      testWebhookConnectivity(),
      testDatabasePerformance()
    ]);
  }).then(() => {
    console.log('\n🏁 All tests completed.');
    process.exit(0);
  }).catch(error => {
    console.error('Test suite failed:', error);
    process.exit(1);
  });
}