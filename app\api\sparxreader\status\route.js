import { NextResponse } from 'next/server';
import { getGlobalBrowser, getGlobalPage } from '../browser-context.js';

export async function GET(request) {
  try {
    const browser = getGlobalBrowser();
    const page = getGlobalPage();
    
    let browserStatus = 'none';
    let pageStatus = 'none';
    let pageTitle = null;
    let pageUrl = null;
    
    if (browser) {
      try {
        browserStatus = browser._closed ? 'closed' : 'active';
        
        if (page && !page.isClosed()) {
          pageStatus = 'active';
          try {
            pageTitle = await page.title();
            pageUrl = page.url();
          } catch (error) {
            pageStatus = 'error';
            console.log('Error getting page info:', error.message);
          }
        } else {
          pageStatus = page ? 'closed' : 'none';
        }
      } catch (error) {
        browserStatus = 'error';
        console.log('Error checking browser status:', error.message);
      }
    }
    
    const status = {
      browser: browserStatus,
      page: pageStatus,
      pageTitle: pageTitle,
      pageUrl: pageUrl,
      timestamp: new Date().toISOString()
    };
    
    console.log('Browser session status:', status);
    
    return NextResponse.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('Error checking browser status:', error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}