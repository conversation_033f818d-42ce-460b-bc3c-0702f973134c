"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcryptjs";
exports.ids = ["vendor-chunks/bcryptjs"];
exports.modules = {

/***/ "(rsc)/./node_modules/bcryptjs/umd/index.js":
/*!********************************************!*\
  !*** ./node_modules/bcryptjs/umd/index.js ***!
  \********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;\n// GENERATED FILE. DO NOT EDIT.\n(function(global, factory) {\n    function preferDefault(exports1) {\n        return exports1.default || exports1;\n    }\n    if (true) {\n        !(__WEBPACK_AMD_DEFINE_ARRAY__ = [\n            __webpack_require__(/*! crypto */ \"crypto\")\n        ], __WEBPACK_AMD_DEFINE_RESULT__ = (function(_crypto) {\n            var exports1 = {};\n            factory(exports1, _crypto);\n            return preferDefault(exports1);\n        }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    } else {}\n})(typeof globalThis !== \"undefined\" ? globalThis : typeof self !== \"undefined\" ? self : void 0, function(_exports, _crypto) {\n    \"use strict\";\n    Object.defineProperty(_exports, \"__esModule\", {\n        value: true\n    });\n    _exports.compare = compare;\n    _exports.compareSync = compareSync;\n    _exports.decodeBase64 = decodeBase64;\n    _exports.default = void 0;\n    _exports.encodeBase64 = encodeBase64;\n    _exports.genSalt = genSalt;\n    _exports.genSaltSync = genSaltSync;\n    _exports.getRounds = getRounds;\n    _exports.getSalt = getSalt;\n    _exports.hash = hash;\n    _exports.hashSync = hashSync;\n    _exports.setRandomFallback = setRandomFallback;\n    _exports.truncates = truncates;\n    _crypto = _interopRequireDefault(_crypto);\n    function _interopRequireDefault(e) {\n        return e && e.__esModule ? e : {\n            default: e\n        };\n    }\n    /*\n   Copyright (c) 2012 Nevins Bartolomeo <<EMAIL>>\n   Copyright (c) 2012 Shane Girish <<EMAIL>>\n   Copyright (c) 2025 Daniel Wirtz <<EMAIL>>\n  \n   Redistribution and use in source and binary forms, with or without\n   modification, are permitted provided that the following conditions\n   are met:\n   1. Redistributions of source code must retain the above copyright\n   notice, this list of conditions and the following disclaimer.\n   2. Redistributions in binary form must reproduce the above copyright\n   notice, this list of conditions and the following disclaimer in the\n   documentation and/or other materials provided with the distribution.\n   3. The name of the author may not be used to endorse or promote products\n   derived from this software without specific prior written permission.\n  \n   THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR\n   IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES\n   OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.\n   IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,\n   INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT\n   NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,\n   DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY\n   THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n   (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n   THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n   */ // The Node.js crypto module is used as a fallback for the Web Crypto API. When\n    // building for the browser, inclusion of the crypto module should be disabled,\n    // which the package hints at in its package.json for bundlers that support it.\n    /**\n     * The random implementation to use as a fallback.\n     * @type {?function(number):!Array.<number>}\n     * @inner\n     */ var randomFallback = null;\n    /**\n     * Generates cryptographically secure random bytes.\n     * @function\n     * @param {number} len Bytes length\n     * @returns {!Array.<number>} Random bytes\n     * @throws {Error} If no random implementation is available\n     * @inner\n     */ function randomBytes(len) {\n        // Web Crypto API. Globally available in the browser and in Node.js >=23.\n        try {\n            return crypto.getRandomValues(new Uint8Array(len));\n        } catch  {}\n        // Node.js crypto module for non-browser environments.\n        try {\n            return _crypto.default.randomBytes(len);\n        } catch  {}\n        // Custom fallback specified with `setRandomFallback`.\n        if (!randomFallback) {\n            throw Error(\"Neither WebCryptoAPI nor a crypto module is available. Use bcrypt.setRandomFallback to set an alternative\");\n        }\n        return randomFallback(len);\n    }\n    /**\n     * Sets the pseudo random number generator to use as a fallback if neither node's `crypto` module nor the Web Crypto\n     *  API is available. Please note: It is highly important that the PRNG used is cryptographically secure and that it\n     *  is seeded properly!\n     * @param {?function(number):!Array.<number>} random Function taking the number of bytes to generate as its\n     *  sole argument, returning the corresponding array of cryptographically secure random byte values.\n     * @see http://nodejs.org/api/crypto.html\n     * @see http://www.w3.org/TR/WebCryptoAPI/\n     */ function setRandomFallback(random) {\n        randomFallback = random;\n    }\n    /**\n     * Synchronously generates a salt.\n     * @param {number=} rounds Number of rounds to use, defaults to 10 if omitted\n     * @param {number=} seed_length Not supported.\n     * @returns {string} Resulting salt\n     * @throws {Error} If a random fallback is required but not set\n     */ function genSaltSync(rounds, seed_length) {\n        rounds = rounds || GENSALT_DEFAULT_LOG2_ROUNDS;\n        if (typeof rounds !== \"number\") throw Error(\"Illegal arguments: \" + typeof rounds + \", \" + typeof seed_length);\n        if (rounds < 4) rounds = 4;\n        else if (rounds > 31) rounds = 31;\n        var salt = [];\n        salt.push(\"$2b$\");\n        if (rounds < 10) salt.push(\"0\");\n        salt.push(rounds.toString());\n        salt.push(\"$\");\n        salt.push(base64_encode(randomBytes(BCRYPT_SALT_LEN), BCRYPT_SALT_LEN)); // May throw\n        return salt.join(\"\");\n    }\n    /**\n     * Asynchronously generates a salt.\n     * @param {(number|function(Error, string=))=} rounds Number of rounds to use, defaults to 10 if omitted\n     * @param {(number|function(Error, string=))=} seed_length Not supported.\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting salt\n     * @returns {!Promise} If `callback` has been omitted\n     * @throws {Error} If `callback` is present but not a function\n     */ function genSalt(rounds, seed_length, callback) {\n        if (typeof seed_length === \"function\") callback = seed_length, seed_length = undefined; // Not supported.\n        if (typeof rounds === \"function\") callback = rounds, rounds = undefined;\n        if (typeof rounds === \"undefined\") rounds = GENSALT_DEFAULT_LOG2_ROUNDS;\n        else if (typeof rounds !== \"number\") throw Error(\"illegal arguments: \" + typeof rounds);\n        function _async(callback) {\n            nextTick(function() {\n                // Pretty thin, but salting is fast enough\n                try {\n                    callback(null, genSaltSync(rounds));\n                } catch (err) {\n                    callback(err);\n                }\n            });\n        }\n        if (callback) {\n            if (typeof callback !== \"function\") throw Error(\"Illegal callback: \" + typeof callback);\n            _async(callback);\n        } else return new Promise(function(resolve, reject) {\n            _async(function(err, res) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Synchronously generates a hash for the given password.\n     * @param {string} password Password to hash\n     * @param {(number|string)=} salt Salt length to generate or salt to use, default to 10\n     * @returns {string} Resulting hash\n     */ function hashSync(password, salt) {\n        if (typeof salt === \"undefined\") salt = GENSALT_DEFAULT_LOG2_ROUNDS;\n        if (typeof salt === \"number\") salt = genSaltSync(salt);\n        if (typeof password !== \"string\" || typeof salt !== \"string\") throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt);\n        return _hash(password, salt);\n    }\n    /**\n     * Asynchronously generates a hash for the given password.\n     * @param {string} password Password to hash\n     * @param {number|string} salt Salt length to generate or salt to use\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n     * @returns {!Promise} If `callback` has been omitted\n     * @throws {Error} If `callback` is present but not a function\n     */ function hash(password, salt, callback, progressCallback) {\n        function _async(callback) {\n            if (typeof password === \"string\" && typeof salt === \"number\") genSalt(salt, function(err, salt) {\n                _hash(password, salt, callback, progressCallback);\n            });\n            else if (typeof password === \"string\" && typeof salt === \"string\") _hash(password, salt, callback, progressCallback);\n            else nextTick(callback.bind(this, Error(\"Illegal arguments: \" + typeof password + \", \" + typeof salt)));\n        }\n        if (callback) {\n            if (typeof callback !== \"function\") throw Error(\"Illegal callback: \" + typeof callback);\n            _async(callback);\n        } else return new Promise(function(resolve, reject) {\n            _async(function(err, res) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Compares two strings of the same length in constant time.\n     * @param {string} known Must be of the correct length\n     * @param {string} unknown Must be the same length as `known`\n     * @returns {boolean}\n     * @inner\n     */ function safeStringCompare(known, unknown) {\n        var diff = known.length ^ unknown.length;\n        for(var i = 0; i < known.length; ++i){\n            diff |= known.charCodeAt(i) ^ unknown.charCodeAt(i);\n        }\n        return diff === 0;\n    }\n    /**\n     * Synchronously tests a password against a hash.\n     * @param {string} password Password to compare\n     * @param {string} hash Hash to test against\n     * @returns {boolean} true if matching, otherwise false\n     * @throws {Error} If an argument is illegal\n     */ function compareSync(password, hash) {\n        if (typeof password !== \"string\" || typeof hash !== \"string\") throw Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hash);\n        if (hash.length !== 60) return false;\n        return safeStringCompare(hashSync(password, hash.substring(0, hash.length - 31)), hash);\n    }\n    /**\n     * Asynchronously tests a password against a hash.\n     * @param {string} password Password to compare\n     * @param {string} hashValue Hash to test against\n     * @param {function(Error, boolean)=} callback Callback receiving the error, if any, otherwise the result\n     * @param {function(number)=} progressCallback Callback successively called with the percentage of rounds completed\n     *  (0.0 - 1.0), maximally once per `MAX_EXECUTION_TIME = 100` ms.\n     * @returns {!Promise} If `callback` has been omitted\n     * @throws {Error} If `callback` is present but not a function\n     */ function compare(password, hashValue, callback, progressCallback) {\n        function _async(callback) {\n            if (typeof password !== \"string\" || typeof hashValue !== \"string\") {\n                nextTick(callback.bind(this, Error(\"Illegal arguments: \" + typeof password + \", \" + typeof hashValue)));\n                return;\n            }\n            if (hashValue.length !== 60) {\n                nextTick(callback.bind(this, null, false));\n                return;\n            }\n            hash(password, hashValue.substring(0, 29), function(err, comp) {\n                if (err) callback(err);\n                else callback(null, safeStringCompare(comp, hashValue));\n            }, progressCallback);\n        }\n        if (callback) {\n            if (typeof callback !== \"function\") throw Error(\"Illegal callback: \" + typeof callback);\n            _async(callback);\n        } else return new Promise(function(resolve, reject) {\n            _async(function(err, res) {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                resolve(res);\n            });\n        });\n    }\n    /**\n     * Gets the number of rounds used to encrypt the specified hash.\n     * @param {string} hash Hash to extract the used number of rounds from\n     * @returns {number} Number of rounds used\n     * @throws {Error} If `hash` is not a string\n     */ function getRounds(hash) {\n        if (typeof hash !== \"string\") throw Error(\"Illegal arguments: \" + typeof hash);\n        return parseInt(hash.split(\"$\")[2], 10);\n    }\n    /**\n     * Gets the salt portion from a hash. Does not validate the hash.\n     * @param {string} hash Hash to extract the salt from\n     * @returns {string} Extracted salt part\n     * @throws {Error} If `hash` is not a string or otherwise invalid\n     */ function getSalt(hash) {\n        if (typeof hash !== \"string\") throw Error(\"Illegal arguments: \" + typeof hash);\n        if (hash.length !== 60) throw Error(\"Illegal hash length: \" + hash.length + \" != 60\");\n        return hash.substring(0, 29);\n    }\n    /**\n     * Tests if a password will be truncated when hashed, that is its length is\n     * greater than 72 bytes when converted to UTF-8.\n     * @param {string} password The password to test\n     * @returns {boolean} `true` if truncated, otherwise `false`\n     */ function truncates(password) {\n        if (typeof password !== \"string\") throw Error(\"Illegal arguments: \" + typeof password);\n        return utf8Length(password) > 72;\n    }\n    /**\n     * Continues with the callback on the next tick.\n     * @function\n     * @param {function(...[*])} callback Callback to execute\n     * @inner\n     */ var nextTick = typeof process !== \"undefined\" && process && typeof process.nextTick === \"function\" ? typeof setImmediate === \"function\" ? setImmediate : process.nextTick : setTimeout;\n    /** Calculates the byte length of a string encoded as UTF8. */ function utf8Length(string) {\n        var len = 0, c = 0;\n        for(var i = 0; i < string.length; ++i){\n            c = string.charCodeAt(i);\n            if (c < 128) len += 1;\n            else if (c < 2048) len += 2;\n            else if ((c & 0xfc00) === 0xd800 && (string.charCodeAt(i + 1) & 0xfc00) === 0xdc00) {\n                ++i;\n                len += 4;\n            } else len += 3;\n        }\n        return len;\n    }\n    /** Converts a string to an array of UTF8 bytes. */ function utf8Array(string) {\n        var offset = 0, c1, c2;\n        var buffer = new Array(utf8Length(string));\n        for(var i = 0, k = string.length; i < k; ++i){\n            c1 = string.charCodeAt(i);\n            if (c1 < 128) {\n                buffer[offset++] = c1;\n            } else if (c1 < 2048) {\n                buffer[offset++] = c1 >> 6 | 192;\n                buffer[offset++] = c1 & 63 | 128;\n            } else if ((c1 & 0xfc00) === 0xd800 && ((c2 = string.charCodeAt(i + 1)) & 0xfc00) === 0xdc00) {\n                c1 = 0x10000 + ((c1 & 0x03ff) << 10) + (c2 & 0x03ff);\n                ++i;\n                buffer[offset++] = c1 >> 18 | 240;\n                buffer[offset++] = c1 >> 12 & 63 | 128;\n                buffer[offset++] = c1 >> 6 & 63 | 128;\n                buffer[offset++] = c1 & 63 | 128;\n            } else {\n                buffer[offset++] = c1 >> 12 | 224;\n                buffer[offset++] = c1 >> 6 & 63 | 128;\n                buffer[offset++] = c1 & 63 | 128;\n            }\n        }\n        return buffer;\n    }\n    // A base64 implementation for the bcrypt algorithm. This is partly non-standard.\n    /**\n     * bcrypt's own non-standard base64 dictionary.\n     * @type {!Array.<string>}\n     * @const\n     * @inner\n     **/ var BASE64_CODE = \"./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\".split(\"\");\n    /**\n     * @type {!Array.<number>}\n     * @const\n     * @inner\n     **/ var BASE64_INDEX = [\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        0,\n        1,\n        54,\n        55,\n        56,\n        57,\n        58,\n        59,\n        60,\n        61,\n        62,\n        63,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        2,\n        3,\n        4,\n        5,\n        6,\n        7,\n        8,\n        9,\n        10,\n        11,\n        12,\n        13,\n        14,\n        15,\n        16,\n        17,\n        18,\n        19,\n        20,\n        21,\n        22,\n        23,\n        24,\n        25,\n        26,\n        27,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1,\n        28,\n        29,\n        30,\n        31,\n        32,\n        33,\n        34,\n        35,\n        36,\n        37,\n        38,\n        39,\n        40,\n        41,\n        42,\n        43,\n        44,\n        45,\n        46,\n        47,\n        48,\n        49,\n        50,\n        51,\n        52,\n        53,\n        -1,\n        -1,\n        -1,\n        -1,\n        -1\n    ];\n    /**\n     * Encodes a byte array to base64 with up to len bytes of input.\n     * @param {!Array.<number>} b Byte array\n     * @param {number} len Maximum input length\n     * @returns {string}\n     * @inner\n     */ function base64_encode(b, len) {\n        var off = 0, rs = [], c1, c2;\n        if (len <= 0 || len > b.length) throw Error(\"Illegal len: \" + len);\n        while(off < len){\n            c1 = b[off++] & 0xff;\n            rs.push(BASE64_CODE[c1 >> 2 & 0x3f]);\n            c1 = (c1 & 0x03) << 4;\n            if (off >= len) {\n                rs.push(BASE64_CODE[c1 & 0x3f]);\n                break;\n            }\n            c2 = b[off++] & 0xff;\n            c1 |= c2 >> 4 & 0x0f;\n            rs.push(BASE64_CODE[c1 & 0x3f]);\n            c1 = (c2 & 0x0f) << 2;\n            if (off >= len) {\n                rs.push(BASE64_CODE[c1 & 0x3f]);\n                break;\n            }\n            c2 = b[off++] & 0xff;\n            c1 |= c2 >> 6 & 0x03;\n            rs.push(BASE64_CODE[c1 & 0x3f]);\n            rs.push(BASE64_CODE[c2 & 0x3f]);\n        }\n        return rs.join(\"\");\n    }\n    /**\n     * Decodes a base64 encoded string to up to len bytes of output.\n     * @param {string} s String to decode\n     * @param {number} len Maximum output length\n     * @returns {!Array.<number>}\n     * @inner\n     */ function base64_decode(s, len) {\n        var off = 0, slen = s.length, olen = 0, rs = [], c1, c2, c3, c4, o, code;\n        if (len <= 0) throw Error(\"Illegal len: \" + len);\n        while(off < slen - 1 && olen < len){\n            code = s.charCodeAt(off++);\n            c1 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n            code = s.charCodeAt(off++);\n            c2 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n            if (c1 == -1 || c2 == -1) break;\n            o = c1 << 2 >>> 0;\n            o |= (c2 & 0x30) >> 4;\n            rs.push(String.fromCharCode(o));\n            if (++olen >= len || off >= slen) break;\n            code = s.charCodeAt(off++);\n            c3 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n            if (c3 == -1) break;\n            o = (c2 & 0x0f) << 4 >>> 0;\n            o |= (c3 & 0x3c) >> 2;\n            rs.push(String.fromCharCode(o));\n            if (++olen >= len || off >= slen) break;\n            code = s.charCodeAt(off++);\n            c4 = code < BASE64_INDEX.length ? BASE64_INDEX[code] : -1;\n            o = (c3 & 0x03) << 6 >>> 0;\n            o |= c4;\n            rs.push(String.fromCharCode(o));\n            ++olen;\n        }\n        var res = [];\n        for(off = 0; off < olen; off++)res.push(rs[off].charCodeAt(0));\n        return res;\n    }\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */ var BCRYPT_SALT_LEN = 16;\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */ var GENSALT_DEFAULT_LOG2_ROUNDS = 10;\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */ var BLOWFISH_NUM_ROUNDS = 16;\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */ var MAX_EXECUTION_TIME = 100;\n    /**\n     * @type {Array.<number>}\n     * @const\n     * @inner\n     */ var P_ORIG = [\n        0x243f6a88,\n        0x85a308d3,\n        0x13198a2e,\n        0x03707344,\n        0xa4093822,\n        0x299f31d0,\n        0x082efa98,\n        0xec4e6c89,\n        0x452821e6,\n        0x38d01377,\n        0xbe5466cf,\n        0x34e90c6c,\n        0xc0ac29b7,\n        0xc97c50dd,\n        0x3f84d5b5,\n        0xb5470917,\n        0x9216d5d9,\n        0x8979fb1b\n    ];\n    /**\n     * @type {Array.<number>}\n     * @const\n     * @inner\n     */ var S_ORIG = [\n        0xd1310ba6,\n        0x98dfb5ac,\n        0x2ffd72db,\n        0xd01adfb7,\n        0xb8e1afed,\n        0x6a267e96,\n        0xba7c9045,\n        0xf12c7f99,\n        0x24a19947,\n        0xb3916cf7,\n        0x0801f2e2,\n        0x858efc16,\n        0x636920d8,\n        0x71574e69,\n        0xa458fea3,\n        0xf4933d7e,\n        0x0d95748f,\n        0x728eb658,\n        0x718bcd58,\n        0x82154aee,\n        0x7b54a41d,\n        0xc25a59b5,\n        0x9c30d539,\n        0x2af26013,\n        0xc5d1b023,\n        0x286085f0,\n        0xca417918,\n        0xb8db38ef,\n        0x8e79dcb0,\n        0x603a180e,\n        0x6c9e0e8b,\n        0xb01e8a3e,\n        0xd71577c1,\n        0xbd314b27,\n        0x78af2fda,\n        0x55605c60,\n        0xe65525f3,\n        0xaa55ab94,\n        0x57489862,\n        0x63e81440,\n        0x55ca396a,\n        0x2aab10b6,\n        0xb4cc5c34,\n        0x1141e8ce,\n        0xa15486af,\n        0x7c72e993,\n        0xb3ee1411,\n        0x636fbc2a,\n        0x2ba9c55d,\n        0x741831f6,\n        0xce5c3e16,\n        0x9b87931e,\n        0xafd6ba33,\n        0x6c24cf5c,\n        0x7a325381,\n        0x28958677,\n        0x3b8f4898,\n        0x6b4bb9af,\n        0xc4bfe81b,\n        0x66282193,\n        0x61d809cc,\n        0xfb21a991,\n        0x487cac60,\n        0x5dec8032,\n        0xef845d5d,\n        0xe98575b1,\n        0xdc262302,\n        0xeb651b88,\n        0x23893e81,\n        0xd396acc5,\n        0x0f6d6ff3,\n        0x83f44239,\n        0x2e0b4482,\n        0xa4842004,\n        0x69c8f04a,\n        0x9e1f9b5e,\n        0x21c66842,\n        0xf6e96c9a,\n        0x670c9c61,\n        0xabd388f0,\n        0x6a51a0d2,\n        0xd8542f68,\n        0x960fa728,\n        0xab5133a3,\n        0x6eef0b6c,\n        0x137a3be4,\n        0xba3bf050,\n        0x7efb2a98,\n        0xa1f1651d,\n        0x39af0176,\n        0x66ca593e,\n        0x82430e88,\n        0x8cee8619,\n        0x456f9fb4,\n        0x7d84a5c3,\n        0x3b8b5ebe,\n        0xe06f75d8,\n        0x85c12073,\n        0x401a449f,\n        0x56c16aa6,\n        0x4ed3aa62,\n        0x363f7706,\n        0x1bfedf72,\n        0x429b023d,\n        0x37d0d724,\n        0xd00a1248,\n        0xdb0fead3,\n        0x49f1c09b,\n        0x075372c9,\n        0x80991b7b,\n        0x25d479d8,\n        0xf6e8def7,\n        0xe3fe501a,\n        0xb6794c3b,\n        0x976ce0bd,\n        0x04c006ba,\n        0xc1a94fb6,\n        0x409f60c4,\n        0x5e5c9ec2,\n        0x196a2463,\n        0x68fb6faf,\n        0x3e6c53b5,\n        0x1339b2eb,\n        0x3b52ec6f,\n        0x6dfc511f,\n        0x9b30952c,\n        0xcc814544,\n        0xaf5ebd09,\n        0xbee3d004,\n        0xde334afd,\n        0x660f2807,\n        0x192e4bb3,\n        0xc0cba857,\n        0x45c8740f,\n        0xd20b5f39,\n        0xb9d3fbdb,\n        0x5579c0bd,\n        0x1a60320a,\n        0xd6a100c6,\n        0x402c7279,\n        0x679f25fe,\n        0xfb1fa3cc,\n        0x8ea5e9f8,\n        0xdb3222f8,\n        0x3c7516df,\n        0xfd616b15,\n        0x2f501ec8,\n        0xad0552ab,\n        0x323db5fa,\n        0xfd238760,\n        0x53317b48,\n        0x3e00df82,\n        0x9e5c57bb,\n        0xca6f8ca0,\n        0x1a87562e,\n        0xdf1769db,\n        0xd542a8f6,\n        0x287effc3,\n        0xac6732c6,\n        0x8c4f5573,\n        0x695b27b0,\n        0xbbca58c8,\n        0xe1ffa35d,\n        0xb8f011a0,\n        0x10fa3d98,\n        0xfd2183b8,\n        0x4afcb56c,\n        0x2dd1d35b,\n        0x9a53e479,\n        0xb6f84565,\n        0xd28e49bc,\n        0x4bfb9790,\n        0xe1ddf2da,\n        0xa4cb7e33,\n        0x62fb1341,\n        0xcee4c6e8,\n        0xef20cada,\n        0x36774c01,\n        0xd07e9efe,\n        0x2bf11fb4,\n        0x95dbda4d,\n        0xae909198,\n        0xeaad8e71,\n        0x6b93d5a0,\n        0xd08ed1d0,\n        0xafc725e0,\n        0x8e3c5b2f,\n        0x8e7594b7,\n        0x8ff6e2fb,\n        0xf2122b64,\n        0x8888b812,\n        0x900df01c,\n        0x4fad5ea0,\n        0x688fc31c,\n        0xd1cff191,\n        0xb3a8c1ad,\n        0x2f2f2218,\n        0xbe0e1777,\n        0xea752dfe,\n        0x8b021fa1,\n        0xe5a0cc0f,\n        0xb56f74e8,\n        0x18acf3d6,\n        0xce89e299,\n        0xb4a84fe0,\n        0xfd13e0b7,\n        0x7cc43b81,\n        0xd2ada8d9,\n        0x165fa266,\n        0x80957705,\n        0x93cc7314,\n        0x211a1477,\n        0xe6ad2065,\n        0x77b5fa86,\n        0xc75442f5,\n        0xfb9d35cf,\n        0xebcdaf0c,\n        0x7b3e89a0,\n        0xd6411bd3,\n        0xae1e7e49,\n        0x00250e2d,\n        0x2071b35e,\n        0x226800bb,\n        0x57b8e0af,\n        0x2464369b,\n        0xf009b91e,\n        0x5563911d,\n        0x59dfa6aa,\n        0x78c14389,\n        0xd95a537f,\n        0x207d5ba2,\n        0x02e5b9c5,\n        0x83260376,\n        0x6295cfa9,\n        0x11c81968,\n        0x4e734a41,\n        0xb3472dca,\n        0x7b14a94a,\n        0x1b510052,\n        0x9a532915,\n        0xd60f573f,\n        0xbc9bc6e4,\n        0x2b60a476,\n        0x81e67400,\n        0x08ba6fb5,\n        0x571be91f,\n        0xf296ec6b,\n        0x2a0dd915,\n        0xb6636521,\n        0xe7b9f9b6,\n        0xff34052e,\n        0xc5855664,\n        0x53b02d5d,\n        0xa99f8fa1,\n        0x08ba4799,\n        0x6e85076a,\n        0x4b7a70e9,\n        0xb5b32944,\n        0xdb75092e,\n        0xc4192623,\n        0xad6ea6b0,\n        0x49a7df7d,\n        0x9cee60b8,\n        0x8fedb266,\n        0xecaa8c71,\n        0x699a17ff,\n        0x5664526c,\n        0xc2b19ee1,\n        0x193602a5,\n        0x75094c29,\n        0xa0591340,\n        0xe4183a3e,\n        0x3f54989a,\n        0x5b429d65,\n        0x6b8fe4d6,\n        0x99f73fd6,\n        0xa1d29c07,\n        0xefe830f5,\n        0x4d2d38e6,\n        0xf0255dc1,\n        0x4cdd2086,\n        0x8470eb26,\n        0x6382e9c6,\n        0x021ecc5e,\n        0x09686b3f,\n        0x3ebaefc9,\n        0x3c971814,\n        0x6b6a70a1,\n        0x687f3584,\n        0x52a0e286,\n        0xb79c5305,\n        0xaa500737,\n        0x3e07841c,\n        0x7fdeae5c,\n        0x8e7d44ec,\n        0x5716f2b8,\n        0xb03ada37,\n        0xf0500c0d,\n        0xf01c1f04,\n        0x0200b3ff,\n        0xae0cf51a,\n        0x3cb574b2,\n        0x25837a58,\n        0xdc0921bd,\n        0xd19113f9,\n        0x7ca92ff6,\n        0x94324773,\n        0x22f54701,\n        0x3ae5e581,\n        0x37c2dadc,\n        0xc8b57634,\n        0x9af3dda7,\n        0xa9446146,\n        0x0fd0030e,\n        0xecc8c73e,\n        0xa4751e41,\n        0xe238cd99,\n        0x3bea0e2f,\n        0x3280bba1,\n        0x183eb331,\n        0x4e548b38,\n        0x4f6db908,\n        0x6f420d03,\n        0xf60a04bf,\n        0x2cb81290,\n        0x24977c79,\n        0x5679b072,\n        0xbcaf89af,\n        0xde9a771f,\n        0xd9930810,\n        0xb38bae12,\n        0xdccf3f2e,\n        0x5512721f,\n        0x2e6b7124,\n        0x501adde6,\n        0x9f84cd87,\n        0x7a584718,\n        0x7408da17,\n        0xbc9f9abc,\n        0xe94b7d8c,\n        0xec7aec3a,\n        0xdb851dfa,\n        0x63094366,\n        0xc464c3d2,\n        0xef1c1847,\n        0x3215d908,\n        0xdd433b37,\n        0x24c2ba16,\n        0x12a14d43,\n        0x2a65c451,\n        0x50940002,\n        0x133ae4dd,\n        0x71dff89e,\n        0x10314e55,\n        0x81ac77d6,\n        0x5f11199b,\n        0x043556f1,\n        0xd7a3c76b,\n        0x3c11183b,\n        0x5924a509,\n        0xf28fe6ed,\n        0x97f1fbfa,\n        0x9ebabf2c,\n        0x1e153c6e,\n        0x86e34570,\n        0xeae96fb1,\n        0x860e5e0a,\n        0x5a3e2ab3,\n        0x771fe71c,\n        0x4e3d06fa,\n        0x2965dcb9,\n        0x99e71d0f,\n        0x803e89d6,\n        0x5266c825,\n        0x2e4cc978,\n        0x9c10b36a,\n        0xc6150eba,\n        0x94e2ea78,\n        0xa5fc3c53,\n        0x1e0a2df4,\n        0xf2f74ea7,\n        0x361d2b3d,\n        0x1939260f,\n        0x19c27960,\n        0x5223a708,\n        0xf71312b6,\n        0xebadfe6e,\n        0xeac31f66,\n        0xe3bc4595,\n        0xa67bc883,\n        0xb17f37d1,\n        0x018cff28,\n        0xc332ddef,\n        0xbe6c5aa5,\n        0x65582185,\n        0x68ab9802,\n        0xeecea50f,\n        0xdb2f953b,\n        0x2aef7dad,\n        0x5b6e2f84,\n        0x1521b628,\n        0x29076170,\n        0xecdd4775,\n        0x619f1510,\n        0x13cca830,\n        0xeb61bd96,\n        0x0334fe1e,\n        0xaa0363cf,\n        0xb5735c90,\n        0x4c70a239,\n        0xd59e9e0b,\n        0xcbaade14,\n        0xeecc86bc,\n        0x60622ca7,\n        0x9cab5cab,\n        0xb2f3846e,\n        0x648b1eaf,\n        0x19bdf0ca,\n        0xa02369b9,\n        0x655abb50,\n        0x40685a32,\n        0x3c2ab4b3,\n        0x319ee9d5,\n        0xc021b8f7,\n        0x9b540b19,\n        0x875fa099,\n        0x95f7997e,\n        0x623d7da8,\n        0xf837889a,\n        0x97e32d77,\n        0x11ed935f,\n        0x16681281,\n        0x0e358829,\n        0xc7e61fd6,\n        0x96dedfa1,\n        0x7858ba99,\n        0x57f584a5,\n        0x1b227263,\n        0x9b83c3ff,\n        0x1ac24696,\n        0xcdb30aeb,\n        0x532e3054,\n        0x8fd948e4,\n        0x6dbc3128,\n        0x58ebf2ef,\n        0x34c6ffea,\n        0xfe28ed61,\n        0xee7c3c73,\n        0x5d4a14d9,\n        0xe864b7e3,\n        0x42105d14,\n        0x203e13e0,\n        0x45eee2b6,\n        0xa3aaabea,\n        0xdb6c4f15,\n        0xfacb4fd0,\n        0xc742f442,\n        0xef6abbb5,\n        0x654f3b1d,\n        0x41cd2105,\n        0xd81e799e,\n        0x86854dc7,\n        0xe44b476a,\n        0x3d816250,\n        0xcf62a1f2,\n        0x5b8d2646,\n        0xfc8883a0,\n        0xc1c7b6a3,\n        0x7f1524c3,\n        0x69cb7492,\n        0x47848a0b,\n        0x5692b285,\n        0x095bbf00,\n        0xad19489d,\n        0x1462b174,\n        0x23820e00,\n        0x58428d2a,\n        0x0c55f5ea,\n        0x1dadf43e,\n        0x233f7061,\n        0x3372f092,\n        0x8d937e41,\n        0xd65fecf1,\n        0x6c223bdb,\n        0x7cde3759,\n        0xcbee7460,\n        0x4085f2a7,\n        0xce77326e,\n        0xa6078084,\n        0x19f8509e,\n        0xe8efd855,\n        0x61d99735,\n        0xa969a7aa,\n        0xc50c06c2,\n        0x5a04abfc,\n        0x800bcadc,\n        0x9e447a2e,\n        0xc3453484,\n        0xfdd56705,\n        0x0e1e9ec9,\n        0xdb73dbd3,\n        0x105588cd,\n        0x675fda79,\n        0xe3674340,\n        0xc5c43465,\n        0x713e38d8,\n        0x3d28f89e,\n        0xf16dff20,\n        0x153e21e7,\n        0x8fb03d4a,\n        0xe6e39f2b,\n        0xdb83adf7,\n        0xe93d5a68,\n        0x948140f7,\n        0xf64c261c,\n        0x94692934,\n        0x411520f7,\n        0x7602d4f7,\n        0xbcf46b2e,\n        0xd4a20068,\n        0xd4082471,\n        0x3320f46a,\n        0x43b7d4b7,\n        0x500061af,\n        0x1e39f62e,\n        0x97244546,\n        0x14214f74,\n        0xbf8b8840,\n        0x4d95fc1d,\n        0x96b591af,\n        0x70f4ddd3,\n        0x66a02f45,\n        0xbfbc09ec,\n        0x03bd9785,\n        0x7fac6dd0,\n        0x31cb8504,\n        0x96eb27b3,\n        0x55fd3941,\n        0xda2547e6,\n        0xabca0a9a,\n        0x28507825,\n        0x530429f4,\n        0x0a2c86da,\n        0xe9b66dfb,\n        0x68dc1462,\n        0xd7486900,\n        0x680ec0a4,\n        0x27a18dee,\n        0x4f3ffea2,\n        0xe887ad8c,\n        0xb58ce006,\n        0x7af4d6b6,\n        0xaace1e7c,\n        0xd3375fec,\n        0xce78a399,\n        0x406b2a42,\n        0x20fe9e35,\n        0xd9f385b9,\n        0xee39d7ab,\n        0x3b124e8b,\n        0x1dc9faf7,\n        0x4b6d1856,\n        0x26a36631,\n        0xeae397b2,\n        0x3a6efa74,\n        0xdd5b4332,\n        0x6841e7f7,\n        0xca7820fb,\n        0xfb0af54e,\n        0xd8feb397,\n        0x454056ac,\n        0xba489527,\n        0x55533a3a,\n        0x20838d87,\n        0xfe6ba9b7,\n        0xd096954b,\n        0x55a867bc,\n        0xa1159a58,\n        0xcca92963,\n        0x99e1db33,\n        0xa62a4a56,\n        0x3f3125f9,\n        0x5ef47e1c,\n        0x9029317c,\n        0xfdf8e802,\n        0x04272f70,\n        0x80bb155c,\n        0x05282ce3,\n        0x95c11548,\n        0xe4c66d22,\n        0x48c1133f,\n        0xc70f86dc,\n        0x07f9c9ee,\n        0x41041f0f,\n        0x404779a4,\n        0x5d886e17,\n        0x325f51eb,\n        0xd59bc0d1,\n        0xf2bcc18f,\n        0x41113564,\n        0x257b7834,\n        0x602a9c60,\n        0xdff8e8a3,\n        0x1f636c1b,\n        0x0e12b4c2,\n        0x02e1329e,\n        0xaf664fd1,\n        0xcad18115,\n        0x6b2395e0,\n        0x333e92e1,\n        0x3b240b62,\n        0xeebeb922,\n        0x85b2a20e,\n        0xe6ba0d99,\n        0xde720c8c,\n        0x2da2f728,\n        0xd0127845,\n        0x95b794fd,\n        0x647d0862,\n        0xe7ccf5f0,\n        0x5449a36f,\n        0x877d48fa,\n        0xc39dfd27,\n        0xf33e8d1e,\n        0x0a476341,\n        0x992eff74,\n        0x3a6f6eab,\n        0xf4f8fd37,\n        0xa812dc60,\n        0xa1ebddf8,\n        0x991be14c,\n        0xdb6e6b0d,\n        0xc67b5510,\n        0x6d672c37,\n        0x2765d43b,\n        0xdcd0e804,\n        0xf1290dc7,\n        0xcc00ffa3,\n        0xb5390f92,\n        0x690fed0b,\n        0x667b9ffb,\n        0xcedb7d9c,\n        0xa091cf0b,\n        0xd9155ea3,\n        0xbb132f88,\n        0x515bad24,\n        0x7b9479bf,\n        0x763bd6eb,\n        0x37392eb3,\n        0xcc115979,\n        0x8026e297,\n        0xf42e312d,\n        0x6842ada7,\n        0xc66a2b3b,\n        0x12754ccc,\n        0x782ef11c,\n        0x6a124237,\n        0xb79251e7,\n        0x06a1bbe6,\n        0x4bfb6350,\n        0x1a6b1018,\n        0x11caedfa,\n        0x3d25bdd8,\n        0xe2e1c3c9,\n        0x44421659,\n        0x0a121386,\n        0xd90cec6e,\n        0xd5abea2a,\n        0x64af674e,\n        0xda86a85f,\n        0xbebfe988,\n        0x64e4c3fe,\n        0x9dbc8057,\n        0xf0f7c086,\n        0x60787bf8,\n        0x6003604d,\n        0xd1fd8346,\n        0xf6381fb0,\n        0x7745ae04,\n        0xd736fccc,\n        0x83426b33,\n        0xf01eab71,\n        0xb0804187,\n        0x3c005e5f,\n        0x77a057be,\n        0xbde8ae24,\n        0x55464299,\n        0xbf582e61,\n        0x4e58f48f,\n        0xf2ddfda2,\n        0xf474ef38,\n        0x8789bdc2,\n        0x5366f9c3,\n        0xc8b38e74,\n        0xb475f255,\n        0x46fcd9b9,\n        0x7aeb2661,\n        0x8b1ddf84,\n        0x846a0e79,\n        0x915f95e2,\n        0x466e598e,\n        0x20b45770,\n        0x8cd55591,\n        0xc902de4c,\n        0xb90bace1,\n        0xbb8205d0,\n        0x11a86248,\n        0x7574a99e,\n        0xb77f19b6,\n        0xe0a9dc09,\n        0x662d09a1,\n        0xc4324633,\n        0xe85a1f02,\n        0x09f0be8c,\n        0x4a99a025,\n        0x1d6efe10,\n        0x1ab93d1d,\n        0x0ba5a4df,\n        0xa186f20f,\n        0x2868f169,\n        0xdcb7da83,\n        0x573906fe,\n        0xa1e2ce9b,\n        0x4fcd7f52,\n        0x50115e01,\n        0xa70683fa,\n        0xa002b5c4,\n        0x0de6d027,\n        0x9af88c27,\n        0x773f8641,\n        0xc3604c06,\n        0x61a806b5,\n        0xf0177a28,\n        0xc0f586e0,\n        0x006058aa,\n        0x30dc7d62,\n        0x11e69ed7,\n        0x2338ea63,\n        0x53c2dd94,\n        0xc2c21634,\n        0xbbcbee56,\n        0x90bcb6de,\n        0xebfc7da1,\n        0xce591d76,\n        0x6f05e409,\n        0x4b7c0188,\n        0x39720a3d,\n        0x7c927c24,\n        0x86e3725f,\n        0x724d9db9,\n        0x1ac15bb4,\n        0xd39eb8fc,\n        0xed545578,\n        0x08fca5b5,\n        0xd83d7cd3,\n        0x4dad0fc4,\n        0x1e50ef5e,\n        0xb161e6f8,\n        0xa28514d9,\n        0x6c51133c,\n        0x6fd5c7e7,\n        0x56e14ec4,\n        0x362abfce,\n        0xddc6c837,\n        0xd79a3234,\n        0x92638212,\n        0x670efa8e,\n        0x406000e0,\n        0x3a39ce37,\n        0xd3faf5cf,\n        0xabc27737,\n        0x5ac52d1b,\n        0x5cb0679e,\n        0x4fa33742,\n        0xd3822740,\n        0x99bc9bbe,\n        0xd5118e9d,\n        0xbf0f7315,\n        0xd62d1c7e,\n        0xc700c47b,\n        0xb78c1b6b,\n        0x21a19045,\n        0xb26eb1be,\n        0x6a366eb4,\n        0x5748ab2f,\n        0xbc946e79,\n        0xc6a376d2,\n        0x6549c2c8,\n        0x530ff8ee,\n        0x468dde7d,\n        0xd5730a1d,\n        0x4cd04dc6,\n        0x2939bbdb,\n        0xa9ba4650,\n        0xac9526e8,\n        0xbe5ee304,\n        0xa1fad5f0,\n        0x6a2d519a,\n        0x63ef8ce2,\n        0x9a86ee22,\n        0xc089c2b8,\n        0x43242ef6,\n        0xa51e03aa,\n        0x9cf2d0a4,\n        0x83c061ba,\n        0x9be96a4d,\n        0x8fe51550,\n        0xba645bd6,\n        0x2826a2f9,\n        0xa73a3ae1,\n        0x4ba99586,\n        0xef5562e9,\n        0xc72fefd3,\n        0xf752f7da,\n        0x3f046f69,\n        0x77fa0a59,\n        0x80e4a915,\n        0x87b08601,\n        0x9b09e6ad,\n        0x3b3ee593,\n        0xe990fd5a,\n        0x9e34d797,\n        0x2cf0b7d9,\n        0x022b8b51,\n        0x96d5ac3a,\n        0x017da67d,\n        0xd1cf3ed6,\n        0x7c7d2d28,\n        0x1f9f25cf,\n        0xadf2b89b,\n        0x5ad6b472,\n        0x5a88f54c,\n        0xe029ac71,\n        0xe019a5e6,\n        0x47b0acfd,\n        0xed93fa9b,\n        0xe8d3c48d,\n        0x283b57cc,\n        0xf8d56629,\n        0x79132e28,\n        0x785f0191,\n        0xed756055,\n        0xf7960e44,\n        0xe3d35e8c,\n        0x15056dd4,\n        0x88f46dba,\n        0x03a16125,\n        0x0564f0bd,\n        0xc3eb9e15,\n        0x3c9057a2,\n        0x97271aec,\n        0xa93a072a,\n        0x1b3f6d9b,\n        0x1e6321f5,\n        0xf59c66fb,\n        0x26dcf319,\n        0x7533d928,\n        0xb155fdf5,\n        0x03563482,\n        0x8aba3cbb,\n        0x28517711,\n        0xc20ad9f8,\n        0xabcc5167,\n        0xccad925f,\n        0x4de81751,\n        0x3830dc8e,\n        0x379d5862,\n        0x9320f991,\n        0xea7a90c2,\n        0xfb3e7bce,\n        0x5121ce64,\n        0x774fbe32,\n        0xa8b6e37e,\n        0xc3293d46,\n        0x48de5369,\n        0x6413e680,\n        0xa2ae0810,\n        0xdd6db224,\n        0x69852dfd,\n        0x09072166,\n        0xb39a460a,\n        0x6445c0dd,\n        0x586cdecf,\n        0x1c20c8ae,\n        0x5bbef7dd,\n        0x1b588d40,\n        0xccd2017f,\n        0x6bb4e3bb,\n        0xdda26a7e,\n        0x3a59ff45,\n        0x3e350a44,\n        0xbcb4cdd5,\n        0x72eacea8,\n        0xfa6484bb,\n        0x8d6612ae,\n        0xbf3c6f47,\n        0xd29be463,\n        0x542f5d9e,\n        0xaec2771b,\n        0xf64e6370,\n        0x740e0d8d,\n        0xe75b1357,\n        0xf8721671,\n        0xaf537d5d,\n        0x4040cb08,\n        0x4eb4e2cc,\n        0x34d2466a,\n        0x0115af84,\n        0xe1b00428,\n        0x95983a1d,\n        0x06b89fb4,\n        0xce6ea048,\n        0x6f3f3b82,\n        0x3520ab82,\n        0x011a1d4b,\n        0x277227f8,\n        0x611560b1,\n        0xe7933fdc,\n        0xbb3a792b,\n        0x344525bd,\n        0xa08839e1,\n        0x51ce794b,\n        0x2f32c9b7,\n        0xa01fbac9,\n        0xe01cc87e,\n        0xbcc7d1f6,\n        0xcf0111c3,\n        0xa1e8aac7,\n        0x1a908749,\n        0xd44fbd9a,\n        0xd0dadecb,\n        0xd50ada38,\n        0x0339c32a,\n        0xc6913667,\n        0x8df9317c,\n        0xe0b12b4f,\n        0xf79e59b7,\n        0x43f5bb3a,\n        0xf2d519ff,\n        0x27d9459c,\n        0xbf97222c,\n        0x15e6fc2a,\n        0x0f91fc71,\n        0x9b941525,\n        0xfae59361,\n        0xceb69ceb,\n        0xc2a86459,\n        0x12baa8d1,\n        0xb6c1075e,\n        0xe3056a0c,\n        0x10d25065,\n        0xcb03a442,\n        0xe0ec6e0e,\n        0x1698db3b,\n        0x4c98a0be,\n        0x3278e964,\n        0x9f1f9532,\n        0xe0d392df,\n        0xd3a0342b,\n        0x8971f21e,\n        0x1b0a7441,\n        0x4ba3348c,\n        0xc5be7120,\n        0xc37632d8,\n        0xdf359f8d,\n        0x9b992f2e,\n        0xe60b6f47,\n        0x0fe3f11d,\n        0xe54cda54,\n        0x1edad891,\n        0xce6279cf,\n        0xcd3e7e6f,\n        0x1618b166,\n        0xfd2c1d05,\n        0x848fd2c5,\n        0xf6fb2299,\n        0xf523f357,\n        0xa6327623,\n        0x93a83531,\n        0x56cccd02,\n        0xacf08162,\n        0x5a75ebb5,\n        0x6e163697,\n        0x88d273cc,\n        0xde966292,\n        0x81b949d0,\n        0x4c50901b,\n        0x71c65614,\n        0xe6c6c7bd,\n        0x327a140a,\n        0x45e1d006,\n        0xc3f27b9a,\n        0xc9aa53fd,\n        0x62a80f00,\n        0xbb25bfe2,\n        0x35bdd2f6,\n        0x71126905,\n        0xb2040222,\n        0xb6cbcf7c,\n        0xcd769c2b,\n        0x53113ec0,\n        0x1640e3d3,\n        0x38abbd60,\n        0x2547adf0,\n        0xba38209c,\n        0xf746ce76,\n        0x77afa1c5,\n        0x20756060,\n        0x85cbfe4e,\n        0x8ae88dd8,\n        0x7aaaf9b0,\n        0x4cf9aa7e,\n        0x1948c25c,\n        0x02fb8a8c,\n        0x01c36ae4,\n        0xd6ebe1f9,\n        0x90d4f869,\n        0xa65cdea0,\n        0x3f09252d,\n        0xc208e69f,\n        0xb74e6132,\n        0xce77e25b,\n        0x578fdfe3,\n        0x3ac372e6\n    ];\n    /**\n     * @type {Array.<number>}\n     * @const\n     * @inner\n     */ var C_ORIG = [\n        0x4f727068,\n        0x65616e42,\n        0x65686f6c,\n        0x64657253,\n        0x63727944,\n        0x6f756274\n    ];\n    /**\n     * @param {Array.<number>} lr\n     * @param {number} off\n     * @param {Array.<number>} P\n     * @param {Array.<number>} S\n     * @returns {Array.<number>}\n     * @inner\n     */ function _encipher(lr, off, P, S) {\n        // This is our bottleneck: 1714/1905 ticks / 90% - see profile.txt\n        var n, l = lr[off], r = lr[off + 1];\n        l ^= P[0];\n        /*\n      for (var i=0, k=BLOWFISH_NUM_ROUNDS-2; i<=k;)\n          // Feistel substitution on left word\n          n  = S[l >>> 24],\n          n += S[0x100 | ((l >> 16) & 0xff)],\n          n ^= S[0x200 | ((l >> 8) & 0xff)],\n          n += S[0x300 | (l & 0xff)],\n          r ^= n ^ P[++i],\n          // Feistel substitution on right word\n          n  = S[r >>> 24],\n          n += S[0x100 | ((r >> 16) & 0xff)],\n          n ^= S[0x200 | ((r >> 8) & 0xff)],\n          n += S[0x300 | (r & 0xff)],\n          l ^= n ^ P[++i];\n      */ //The following is an unrolled version of the above loop.\n        //Iteration 0\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[1];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[2];\n        //Iteration 1\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[3];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[4];\n        //Iteration 2\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[5];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[6];\n        //Iteration 3\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[7];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[8];\n        //Iteration 4\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[9];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[10];\n        //Iteration 5\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[11];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[12];\n        //Iteration 6\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[13];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[14];\n        //Iteration 7\n        n = S[l >>> 24];\n        n += S[0x100 | l >> 16 & 0xff];\n        n ^= S[0x200 | l >> 8 & 0xff];\n        n += S[0x300 | l & 0xff];\n        r ^= n ^ P[15];\n        n = S[r >>> 24];\n        n += S[0x100 | r >> 16 & 0xff];\n        n ^= S[0x200 | r >> 8 & 0xff];\n        n += S[0x300 | r & 0xff];\n        l ^= n ^ P[16];\n        lr[off] = r ^ P[BLOWFISH_NUM_ROUNDS + 1];\n        lr[off + 1] = l;\n        return lr;\n    }\n    /**\n     * @param {Array.<number>} data\n     * @param {number} offp\n     * @returns {{key: number, offp: number}}\n     * @inner\n     */ function _streamtoword(data, offp) {\n        for(var i = 0, word = 0; i < 4; ++i)word = word << 8 | data[offp] & 0xff, offp = (offp + 1) % data.length;\n        return {\n            key: word,\n            offp: offp\n        };\n    }\n    /**\n     * @param {Array.<number>} key\n     * @param {Array.<number>} P\n     * @param {Array.<number>} S\n     * @inner\n     */ function _key(key, P, S) {\n        var offset = 0, lr = [\n            0,\n            0\n        ], plen = P.length, slen = S.length, sw;\n        for(var i = 0; i < plen; i++)sw = _streamtoword(key, offset), offset = sw.offp, P[i] = P[i] ^ sw.key;\n        for(i = 0; i < plen; i += 2)lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];\n        for(i = 0; i < slen; i += 2)lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];\n    }\n    /**\n     * Expensive key schedule Blowfish.\n     * @param {Array.<number>} data\n     * @param {Array.<number>} key\n     * @param {Array.<number>} P\n     * @param {Array.<number>} S\n     * @inner\n     */ function _ekskey(data, key, P, S) {\n        var offp = 0, lr = [\n            0,\n            0\n        ], plen = P.length, slen = S.length, sw;\n        for(var i = 0; i < plen; i++)sw = _streamtoword(key, offp), offp = sw.offp, P[i] = P[i] ^ sw.key;\n        offp = 0;\n        for(i = 0; i < plen; i += 2)sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), P[i] = lr[0], P[i + 1] = lr[1];\n        for(i = 0; i < slen; i += 2)sw = _streamtoword(data, offp), offp = sw.offp, lr[0] ^= sw.key, sw = _streamtoword(data, offp), offp = sw.offp, lr[1] ^= sw.key, lr = _encipher(lr, 0, P, S), S[i] = lr[0], S[i + 1] = lr[1];\n    }\n    /**\n     * Internaly crypts a string.\n     * @param {Array.<number>} b Bytes to crypt\n     * @param {Array.<number>} salt Salt bytes to use\n     * @param {number} rounds Number of rounds\n     * @param {function(Error, Array.<number>=)=} callback Callback receiving the error, if any, and the resulting bytes. If\n     *  omitted, the operation will be performed synchronously.\n     *  @param {function(number)=} progressCallback Callback called with the current progress\n     * @returns {!Array.<number>|undefined} Resulting bytes if callback has been omitted, otherwise `undefined`\n     * @inner\n     */ function _crypt(b, salt, rounds, callback, progressCallback) {\n        var cdata = C_ORIG.slice(), clen = cdata.length, err;\n        // Validate\n        if (rounds < 4 || rounds > 31) {\n            err = Error(\"Illegal number of rounds (4-31): \" + rounds);\n            if (callback) {\n                nextTick(callback.bind(this, err));\n                return;\n            } else throw err;\n        }\n        if (salt.length !== BCRYPT_SALT_LEN) {\n            err = Error(\"Illegal salt length: \" + salt.length + \" != \" + BCRYPT_SALT_LEN);\n            if (callback) {\n                nextTick(callback.bind(this, err));\n                return;\n            } else throw err;\n        }\n        rounds = 1 << rounds >>> 0;\n        var P, S, i = 0, j;\n        //Use typed arrays when available - huge speedup!\n        if (typeof Int32Array === \"function\") {\n            P = new Int32Array(P_ORIG);\n            S = new Int32Array(S_ORIG);\n        } else {\n            P = P_ORIG.slice();\n            S = S_ORIG.slice();\n        }\n        _ekskey(salt, b, P, S);\n        /**\n       * Calcualtes the next round.\n       * @returns {Array.<number>|undefined} Resulting array if callback has been omitted, otherwise `undefined`\n       * @inner\n       */ function next() {\n            if (progressCallback) progressCallback(i / rounds);\n            if (i < rounds) {\n                var start = Date.now();\n                for(; i < rounds;){\n                    i = i + 1;\n                    _key(b, P, S);\n                    _key(salt, P, S);\n                    if (Date.now() - start > MAX_EXECUTION_TIME) break;\n                }\n            } else {\n                for(i = 0; i < 64; i++)for(j = 0; j < clen >> 1; j++)_encipher(cdata, j << 1, P, S);\n                var ret = [];\n                for(i = 0; i < clen; i++)ret.push((cdata[i] >> 24 & 0xff) >>> 0), ret.push((cdata[i] >> 16 & 0xff) >>> 0), ret.push((cdata[i] >> 8 & 0xff) >>> 0), ret.push((cdata[i] & 0xff) >>> 0);\n                if (callback) {\n                    callback(null, ret);\n                    return;\n                } else return ret;\n            }\n            if (callback) nextTick(next);\n        }\n        // Async\n        if (typeof callback !== \"undefined\") {\n            next();\n        // Sync\n        } else {\n            var res;\n            while(true)if (typeof (res = next()) !== \"undefined\") return res || [];\n        }\n    }\n    /**\n     * Internally hashes a password.\n     * @param {string} password Password to hash\n     * @param {?string} salt Salt to use, actually never null\n     * @param {function(Error, string=)=} callback Callback receiving the error, if any, and the resulting hash. If omitted,\n     *  hashing is performed synchronously.\n     *  @param {function(number)=} progressCallback Callback called with the current progress\n     * @returns {string|undefined} Resulting hash if callback has been omitted, otherwise `undefined`\n     * @inner\n     */ function _hash(password, salt, callback, progressCallback) {\n        var err;\n        if (typeof password !== \"string\" || typeof salt !== \"string\") {\n            err = Error(\"Invalid string / salt: Not a string\");\n            if (callback) {\n                nextTick(callback.bind(this, err));\n                return;\n            } else throw err;\n        }\n        // Validate the salt\n        var minor, offset;\n        if (salt.charAt(0) !== \"$\" || salt.charAt(1) !== \"2\") {\n            err = Error(\"Invalid salt version: \" + salt.substring(0, 2));\n            if (callback) {\n                nextTick(callback.bind(this, err));\n                return;\n            } else throw err;\n        }\n        if (salt.charAt(2) === \"$\") minor = String.fromCharCode(0), offset = 3;\n        else {\n            minor = salt.charAt(2);\n            if (minor !== \"a\" && minor !== \"b\" && minor !== \"y\" || salt.charAt(3) !== \"$\") {\n                err = Error(\"Invalid salt revision: \" + salt.substring(2, 4));\n                if (callback) {\n                    nextTick(callback.bind(this, err));\n                    return;\n                } else throw err;\n            }\n            offset = 4;\n        }\n        // Extract number of rounds\n        if (salt.charAt(offset + 2) > \"$\") {\n            err = Error(\"Missing salt rounds\");\n            if (callback) {\n                nextTick(callback.bind(this, err));\n                return;\n            } else throw err;\n        }\n        var r1 = parseInt(salt.substring(offset, offset + 1), 10) * 10, r2 = parseInt(salt.substring(offset + 1, offset + 2), 10), rounds = r1 + r2, real_salt = salt.substring(offset + 3, offset + 25);\n        password += minor >= \"a\" ? \"\\x00\" : \"\";\n        var passwordb = utf8Array(password), saltb = base64_decode(real_salt, BCRYPT_SALT_LEN);\n        /**\n       * Finishes hashing.\n       * @param {Array.<number>} bytes Byte array\n       * @returns {string}\n       * @inner\n       */ function finish(bytes) {\n            var res = [];\n            res.push(\"$2\");\n            if (minor >= \"a\") res.push(minor);\n            res.push(\"$\");\n            if (rounds < 10) res.push(\"0\");\n            res.push(rounds.toString());\n            res.push(\"$\");\n            res.push(base64_encode(saltb, saltb.length));\n            res.push(base64_encode(bytes, C_ORIG.length * 4 - 1));\n            return res.join(\"\");\n        }\n        // Sync\n        if (typeof callback == \"undefined\") return finish(_crypt(passwordb, saltb, rounds));\n        else {\n            _crypt(passwordb, saltb, rounds, function(err, bytes) {\n                if (err) callback(err, null);\n                else callback(null, finish(bytes));\n            }, progressCallback);\n        }\n    }\n    /**\n     * Encodes a byte array to base64 with up to len bytes of input, using the custom bcrypt alphabet.\n     * @function\n     * @param {!Array.<number>} bytes Byte array\n     * @param {number} length Maximum input length\n     * @returns {string}\n     */ function encodeBase64(bytes, length) {\n        return base64_encode(bytes, length);\n    }\n    /**\n     * Decodes a base64 encoded string to up to len bytes of output, using the custom bcrypt alphabet.\n     * @function\n     * @param {string} string String to decode\n     * @param {number} length Maximum output length\n     * @returns {!Array.<number>}\n     */ function decodeBase64(string, length) {\n        return base64_decode(string, length);\n    }\n    var _default = _exports.default = {\n        setRandomFallback,\n        genSaltSync,\n        genSalt,\n        hashSync,\n        hash,\n        compareSync,\n        compare,\n        getRounds,\n        getSalt,\n        truncates,\n        encodeBase64,\n        decodeBase64\n    };\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/bcryptjs/umd/index.js\n");

/***/ })

};
;