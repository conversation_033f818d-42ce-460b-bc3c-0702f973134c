const { getDatabase } = require('./lib/database');

async function testSchedulingFix() {
  console.log('🧪 Testing Scheduling Access Fix...\n');
  
  try {
    const db = getDatabase();
    
    // Find the test user we just created
    const testUser = db.db.prepare(`
      SELECT * FROM users 
      WHERE username LIKE 'queuetest_%' 
      ORDER BY created_at DESC 
      LIMIT 1
    `).get();
    
    if (!testUser) {
      console.log('❌ No test user found. Run test-queue-system.js first.');
      return;
    }
    
    console.log(`📋 Testing with user: ${testUser.username} (ID: ${testUser.id})`);
    
    // Test license features retrieval
    console.log('\n1. Testing getUserLicenseFeatures...');
    const features = db.getUserLicenseFeatures(testUser.id);
    console.log(`   Max accounts per batch: ${features.max_accounts_per_batch}`);
    console.log(`   Priority level: ${features.priority_level}`);
    console.log(`   Scheduling access: ${features.scheduling_access}`);
    console.log(`   Max batches per day: ${features.max_batches_per_day}`);
    
    if (features.scheduling_access) {
      console.log('   ✅ User has scheduling access');
    } else {
      console.log('   ❌ User does not have scheduling access');
    }
    
    // Test API endpoint simulation
    console.log('\n2. Testing API response simulation...');
    
    // Simulate what the queue status API should return
    const mockApiResponse = {
      user_queue_status: {
        total_batches: 1,
        active_batches: 0,
        completed_batches: 1,
        failed_batches: 0
      },
      queue_positions: [],
      estimated_wait_time: {
        minutes: 0,
        formatted: "No wait"
      },
      license_features: features,
      recent_activity: []
    };
    
    console.log('   Mock API Response license_features:');
    console.log(`   - scheduling_access: ${mockApiResponse.license_features.scheduling_access}`);
    console.log(`   - max_accounts_per_batch: ${mockApiResponse.license_features.max_accounts_per_batch}`);
    console.log(`   - priority_level: ${mockApiResponse.license_features.priority_level}`);
    
    // Test the condition that would show/hide the scheduling popup
    console.log('\n3. Testing scheduling popup condition...');
    const shouldShowSchedulingPopup = !mockApiResponse.license_features?.scheduling_access;
    
    if (shouldShowSchedulingPopup) {
      console.log('   ❌ Scheduling popup WOULD be shown (this is the bug)');
    } else {
      console.log('   ✅ Scheduling popup would NOT be shown (fix working)');
    }
    
    // Test license validation in middleware
    console.log('\n4. Testing middleware license validation...');
    try {
      // This simulates what the middleware does
      if (!features.scheduling_access) {
        throw new Error('Scheduling not available for your license');
      }
      console.log('   ✅ Middleware would allow scheduling access');
    } catch (error) {
      console.log(`   ❌ Middleware would block scheduling: ${error.message}`);
    }
    
    console.log('\n🎉 Scheduling fix test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

if (require.main === module) {
  testSchedulingFix();
}

module.exports = { testSchedulingFix };
