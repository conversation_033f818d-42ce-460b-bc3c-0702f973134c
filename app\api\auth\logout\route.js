import { NextResponse } from 'next/server';
import { getAuthManager } from '../../../../lib/auth';

export async function POST(request) {
  try {
    const auth = getAuthManager();
    
    // Get token from header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    try {
      // Validate session to get user info
      const session = auth.validateSession(token);
      
      // Logout user
      auth.logout(token, session.userId);

      return NextResponse.json({
        success: true,
        message: 'Logged out successfully'
      });

    } catch (authError) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request) {
  // Logout from all sessions
  try {
    const auth = getAuthManager();
    
    // Get token from header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    try {
      // Validate session to get user info
      const session = auth.validateSession(token);
      
      // Logout from all sessions
      auth.logoutAllSessions(session.userId);

      return NextResponse.json({
        success: true,
        message: 'Logged out from all sessions successfully'
      });

    } catch (authError) {
      return NextResponse.json(
        { success: false, error: 'Invalid session' },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('Logout all error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}